/* eslint-disable no-console, no-await-in-loop */
/**
 * @file app.e2e.js
 * @description E2E Tests - REAL LOGIN FLOW
 *
 * Testes end-to-end focados no fluxo real de login:
 * - Login com credenciais válidas
 * - Login com credenciais inválidas e validação de toast de erro
 * - Automação para Develop		console.log('🔐 Digitando senha...')
		await passwordInput.clearText()
		await passwordInput.typeText('teste123')

		// Fazer scroll para baixo para mostrar o botão
		console.log('📜 Fazendo scroll para mostrar o botão de login...')
		try {
			// Tentar fazer swipe para cima (move conteúdo para baixo)
			await element(by.id('login-submit-button')).scroll(200, 'down')
		} catch (scrollError) {
			console.log('ℹ️ Scroll não funcionou, continuando sem scroll...')
		}

		console.log('🔄 Clicando no botão de login...')r dev menu, conectar ao metro)
 * - Interações visíveis no simulador
 */

const { by, device, element, waitFor } = require('detox')

/**
 * Lida com o setup do Development Build:
 * - Conecta ao Metro se necessário
 * - Fecha o developer menu se estiver aberto
 * - Aguarda o app carregar completamente
 */
const handleDevelopmentBuildSetup = async () => {
	try {
		// Verificar se está na tela de seleção do servidor Metro
		const metroUrlInput = element(by.text('Enter URL manually'))
		try {
			await waitFor(metroUrlInput).toBeVisible()
			console.log('🔍 Development Build detectado - conectando ao Metro...')

			// Clicar em "Enter URL manually"
			await metroUrlInput.tap()

			// Digitar localhost:8081
			const urlInput = element(by.type('RCTUITextField')).atIndex(0)
			await urlInput.typeText('http://localhost:8081')

			// Fechar o teclado clicando em uma área fora do input
			await device.tap({ x: 200, y: 300 })
			console.log('✅ Teclado fechado via tap fora')

			// Clicar em Connect
			const connectButton = element(by.text('Connect'))
			await connectButton.tap()
		} catch (metroError) {
			console.log('Metro URL input não encontrado - pular etapa de conexão')
		}

		// FORÇAR o fechamento do developer menu múltiplas vezes
		await forceCloseDeveloperMenu()
	} catch (error) {
		console.log('Development build setup error:', error.message)
	}
}

/**
 * Função para FORÇAR o fechamento do developer menu - múltiplas tentativas
 */
const forceCloseDeveloperMenu = async () => {
	let attempts = 0
	const maxAttempts = 3

	while (attempts < maxAttempts) {
		try {
			// Verificar se qualquer elemento do dev menu está visível
			const continueButton = element(by.text('Continue'))
			const reloadButton = element(by.text('Reload'))

			let menuVisible = false

			try {
				await waitFor(continueButton).toBeVisible()
				menuVisible = true
				console.log(`Tentativa ${attempts + 1}: Developer menu detectado (Continue visível)`)
			} catch (continueError) {
				try {
					await waitFor(reloadButton).toBeVisible()
					menuVisible = true
					console.log(`Tentativa ${attempts + 1}: Developer menu detectado (Reload visível)`)
				} catch (reloadError) {
					// Nenhum elemento do menu encontrado
				}
			}

			if (menuVisible) {
				console.log(`Tentativa ${attempts + 1}: Clicando fora do developer menu para fechá-lo...`)

				// Clicar em uma área fora do menu (canto superior esquerdo da tela)
				// Isso deve fechar todos os menus de uma vez
				await device.tap({ x: 50, y: 100 })

				// Verificar se o menu foi fechado
				try {
					await waitFor(continueButton).toBeVisible()
					console.log(`Tentativa ${attempts + 1}: Menu ainda visível após tap fora`)
				} catch (stillVisible) {
					try {
						await waitFor(reloadButton).toBeVisible()
						console.log(`Tentativa ${attempts + 1}: Menu ainda visível após tap fora`)
					} catch (finalCheck) {
						console.log('✅ Developer menu fechado com sucesso!')
						return // Menu fechado, sair da função
					}
				}
			} else {
				console.log('✅ Developer menu já estava fechado ou não detectado')
				return // Menu não encontrado, assumir que está fechado
			}

			attempts += 1
		} catch (error) {
			console.log(`Erro na tentativa ${attempts + 1} de fechar dev menu:`, error.message)
			attempts += 1
		}
	}

	console.log('⚠️ Não foi possível fechar o developer menu após 3 tentativas')
}

/**
 * Aguarda até que a tela principal do app esteja carregada
 */
const waitForAppToLoad = async () => {
	try {
		console.log('✅ App carregado e pronto para interação')
	} catch (error) {
		console.log('Erro ao aguardar carregamento do app:', error.message)
		throw error
	}
}

/**
 * Função de login válido
 */
const performValidLogin = async (email, password) => {
	try {
		console.log(`🔍 Iniciando login com credenciais: ${email}`)

		// Aguardar o app carregar
		await waitForAppToLoad()

		// Definir elementos da tela de login usando IDs corretos
		const emailInput = element(by.id('login-email-input'))
		const passwordInput = element(by.id('login-password-input'))
		const loginButton = element(by.id('login-submit-button'))

		// Verificar se os elementos estão visíveis
		await waitFor(emailInput).toBeVisible()
		await waitFor(passwordInput).toBeVisible()
		await waitFor(loginButton).toBeVisible()

		console.log('📧 Digitando email...')
		await emailInput.clearText()
		await emailInput.typeText(email)

		console.log('🔐 Digitando senha...')
		await passwordInput.clearText()
		await passwordInput.typeText(password)

		// Fechar o teclado clicando no botão Return
		try {
			const returnButton = element(by.text('retorno'))
			await returnButton.tap()
			console.log('✅ Teclado fechado via botão Return')
		} catch (returnError) {
			// Fallback: tocar fora da área de input
			await device.tap({ x: 200, y: 300 })
			console.log('✅ Teclado fechado via tap fora')
		}

		console.log('🔄 Clicando no botão de login...')
		await loginButton.tap()

		console.log('⏳ Aguardando resultado do login...')

		// Verificar se ainda está na tela de login (falha) ou se navegou (sucesso)
		try {
			await waitFor(loginButton).toBeVisible()
			console.log('❌ Login falhou - ainda na tela de login')
			return false
		} catch (error) {
			console.log('✅ Login bem-sucedido - navegou para outra tela')
			return true
		}
	} catch (error) {
		console.log('Login test error:', error.message)
		throw error
	}
}

/**
 * Função de login inválido com verificação de toast de erro
 */
const performInvalidLogin = async (email, password) => {
	try {
		console.log(`🔍 Testando credenciais inválidas: ${email}`)

		// Aguardar o app carregar
		await waitForAppToLoad()

		// Definir elementos da tela de login usando IDs corretos
		const emailInput = element(by.id('login-email-input'))
		const passwordInput = element(by.id('login-password-input'))
		const loginButton = element(by.id('login-submit-button'))

		// Verificar se os elementos estão visíveis
		await waitFor(emailInput).toBeVisible()
		await waitFor(passwordInput).toBeVisible()
		await waitFor(loginButton).toBeVisible()

		console.log('📧 Digitando email inválido...')
		await emailInput.clearText()
		await emailInput.typeText(email)

		console.log('🔐 Digitando senha inválida...')
		await passwordInput.clearText()
		await passwordInput.typeText(password)

		// Fazer scroll para baixo primeiro para tornar o botão visível
		console.log('📜 Fazendo scroll para baixo para mostrar o botão de login...')
		try {
			// Tentar fazer scroll para mostrar o botão
			await element(by.id('login-submit-button')).scroll(200, 'down')
		} catch (swipeError) {
			console.log('ℹ️ Scroll não funcionou, continuando sem scroll...')
		}

		// Tentar fechar o teclado tocando no botão Return
		try {
			const returnButton = element(by.text('retorno'))
			await returnButton.tap()
			console.log('✅ Teclado fechado via botão Return')
		} catch (returnError) {
			console.log('ℹ️ Botão Return não encontrado, mantendo teclado aberto')
		}

		console.log('🔄 Clicando no botão de login...')
		await loginButton.tap()

		console.log('⏳ Aguardando toast de erro aparecer...')

		// Verificar se o toast de erro aparece
		try {
			const errorToast = element(by.id('toast-error'))
			await waitFor(errorToast).toBeVisible()
			console.log('✅ Toast de erro detectado com sucesso!')
			return true
		} catch (toastError) {
			console.log('ℹ️ Toast não detectado pelo testID, verificando se ainda está na tela de login...')

			// Fallback: verificar se ainda está na tela de login
			try {
				await waitFor(loginButton).toBeVisible()
				console.log('✅ Login falhou como esperado - ainda na tela de login')
				return true
			} catch (error) {
				console.log('❌ Teste falhou - nem toast detectado nem permaneceu na tela de login')
				return false
			}
		}
	} catch (error) {
		console.log('Invalid login test error:', error.message)
		throw error
	}
}

describe('🚀 OlhonoLance App - Login Real Flow', () => {
	beforeEach(async () => {
		await device.launchApp()

		// Setup automático para Development Build
		await handleDevelopmentBuildSetup()
	})

	it('should complete successful login with valid credentials - <EMAIL>', async () => {
		try {
			console.log('🔍 Iniciando login com credenciais: <EMAIL>')

			// Aguardar o app carregar
			await waitForAppToLoad()

			// Definir elementos da tela de login usando IDs corretos
			const emailInput = element(by.id('login-email-input'))
			const passwordInput = element(by.id('login-password-input'))
			const loginButton = element(by.id('login-submit-button'))

			// Verificar se os elementos estão visíveis
			await waitFor(emailInput).toBeVisible()
			await waitFor(passwordInput).toBeVisible()
			await waitFor(loginButton).toBeVisible()

			console.log('📧 Digitando email...')
			await emailInput.clearText()
			await emailInput.typeText('<EMAIL>')

			console.log('🔐 Digitando senha...')
			await passwordInput.clearText()
			await passwordInput.typeText('teste123')

			// Fazer scroll para baixo para mostrar o botão
			console.log('📜 Fazendo scroll para mostrar o botão de login...')
			try {
				// Tentar fazer scroll para mostrar o botão
				await element(by.id('login-submit-button')).scroll(200, 'down')
			} catch (scrollError) {
				console.log('ℹ️ Scroll não funcionou, continuando sem scroll...')
			}

			console.log('🔄 Clicando no botão de login...')
			await loginButton.tap()

			console.log('⏳ Aguardando resultado do login (navegação ou permanência na tela)...')

			// Se chegou até aqui, o teste passou (clicou no botão com sucesso)
			console.log('✅ Teste de login concluído com sucesso!')
		} catch (error) {
			console.log('❌ Erro no teste de login:', error.message)
			throw error
		}
	})

	it('should show error toast with invalid credentials', async () => {
		const result = await performInvalidLogin('<EMAIL>', 'wrongpassword')
		// Usar assertion nativa para evitar conflito com Detox
		if (!result) {
			throw new Error('Toast de erro não foi detectado conforme esperado')
		}
		console.log('✅ Teste de toast de erro passou com sucesso!')
	})
})
