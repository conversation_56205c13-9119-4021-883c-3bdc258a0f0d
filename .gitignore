node_modules/
.expo/
.idea
dist/
npm-debug.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Detox config - specific to local environment
.detoxrc.json
*.orig.*
web-build/

# macOS
.DS_Store

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# Google Services
olhonolance-google-service.json

# iOS and Android build files
ios
android
*.ipa

# Project Tree
project-tree.txt

# vscode
.vscode
yarn.lock
package-lock.json
coverage/

# Test Audit
test-audit-report.json
test-audit-report-minimal.json

# Copilot Instructions
.github/instructions/copilot-instructions.instructions.md
