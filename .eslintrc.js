module.exports = {
	env: {
		browser: true,
		es2021: true,
		jest: true,
	},
	extends: 'airbnb-base',
	parser: '@typescript-eslint/parser',
	overrides: [
		{
			env: {
				node: true,
			},
			files: ['.eslintrc.{js,cjs}'],
			parserOptions: {
				sourceType: 'script',
			},
		},
		{
			files: ['**/__tests__/**/*.js', '**/*.test.js', '**/*.spec.js', 'jest.setup.js'],
			env: {
				jest: true,
			},
			globals: {
				jest: 'readonly',
				describe: 'readonly',
				it: 'readonly',
				expect: 'readonly',
				beforeEach: 'readonly',
				afterEach: 'readonly',
				beforeAll: 'readonly',
				afterAll: 'readonly',
				test: 'readonly',
			},
		},
		{
			files: ['**/*.ts', '**/*.tsx'],
			parser: '@typescript-eslint/parser',
			extends: ['airbnb-base', 'plugin:@typescript-eslint/recommended', 'prettier'],
			plugins: ['@typescript-eslint'],
			parserOptions: {
				ecmaVersion: 'latest',
				sourceType: 'module',
				ecmaFeatures: {
					jsx: true,
				},
				project: './tsconfig.json',
			},
			rules: {
				'@typescript-eslint/no-explicit-any': 'off',
				'@typescript-eslint/no-unused-vars': [
					'error',
					{
						varsIgnorePattern: '.*',
						argsIgnorePattern: '.*',
					},
				],
				'no-unused-vars': 'off', // Desabilita a regra JS em favor da TS
				indent: 'off', // Prettier cuida da indentação
				'@typescript-eslint/indent': 'off', // Prettier cuida da indentação
				quotes: ['error', 'single'],
				semi: ['error', 'never'],
				'import/prefer-default-export': 'off',
				'no-tabs': 'off',
				'import/named': 'error',
				'no-param-reassign': 'off',
				'import/no-cycle': 'off',
				'no-underscore-dangle': 'off',
				'no-restricted-globals': 'off',
				'object-curly-newline': 'off',
				'comma-dangle': 'off', // Prettier cuida disso
				'operator-linebreak': 'off', // Prettier cuida disso
				'implicit-arrow-linebreak': 'off', // Prettier cuida disso
				'import/extensions': [
					'error',
					'ignorePackages',
					{
						js: 'never',
						jsx: 'never',
						ts: 'never',
						tsx: 'never',
					},
				],
				'import/no-extraneous-dependencies': ['error', { devDependencies: true }],
				'max-len': 'off', // Prettier cuida disso
				'no-plusplus': 'off',
				'no-use-before-define': 'off',
				'consistent-return': 'off',
				'no-nested-ternary': 'off',
				'no-console': ['error', { allow: ['warn', 'error'] }],
				'no-prototype-builtins': 'off',
				'sort-imports': [
					'error',
					{
						ignoreCase: false,
						ignoreDeclarationSort: true,
						ignoreMemberSort: false,
						memberSyntaxSortOrder: ['none', 'all', 'multiple', 'single'],
					},
				],
				'linebreak-style': 'off',
				'global-require': 0,
				'import/no-unresolved': ['error', { ignore: ['@env'] }],
			},
		},
	],
	parserOptions: {
		ecmaVersion: 'latest',
		sourceType: 'module',
		ecmaFeatures: {
			jsx: true,
		},
	},
	rules: {
		indent: 'off', // Prettier cuida da indentação
		quotes: ['error', 'single'],
		semi: ['error', 'never'],
		'import/prefer-default-export': 'off',
		'no-tabs': 'off',
		'no-unused-vars': [
			'error',
			{
				varsIgnorePattern: '.*',
				argsIgnorePattern: '.*',
			},
		],
		'import/named': 'error',
		'no-param-reassign': 'off',
		'import/no-cycle': 'off',
		'no-underscore-dangle': 'off',
		'no-restricted-globals': 'off',
		'object-curly-newline': 'off',
		'comma-dangle': 'off', // Prettier cuida disso
		'operator-linebreak': 'off', // Prettier cuida disso
		'implicit-arrow-linebreak': 'off', // Prettier cuida disso
		'import/extensions': 'off',
		'import/no-extraneous-dependencies': ['error', { devDependencies: true }],
		'max-len': 'off', // Prettier cuida disso
		'no-plusplus': 'off',
		'no-use-before-define': 'off',
		'consistent-return': 'off',
		'no-nested-ternary': 'off',
		'no-console': ['error', { allow: ['warn', 'error'] }],
		'no-prototype-builtins': 'off',
		'sort-imports': [
			'error',
			{
				ignoreCase: false,
				ignoreDeclarationSort: true,
				ignoreMemberSort: false,
				memberSyntaxSortOrder: ['none', 'all', 'multiple', 'single'],
			},
		],
		'linebreak-style': 'off',
		'global-require': 0,
		'import/no-unresolved': ['error', { ignore: ['@env'] }],
	},
	settings: {
		'import/resolver': {
			node: {
				extensions: ['.js', '.jsx', '.ts', '.tsx'],
			},
			typescript: {
				alwaysTryTypes: true,
				project: './tsconfig.json',
			},
		},
	},
}
