import { useFonts } from 'expo-font'
import React from 'react'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import MontserratBold from './assets/fonts/MontserratBold.ttf'
import MontserratExtraBold from './assets/fonts/MontserratExtraBold.ttf'
import MontserratLight from './assets/fonts/MontserratLight.ttf'
import MontserratMedium from './assets/fonts/MontserratMedium.ttf'
import Montserrat from './assets/fonts/MontserratRegular.ttf'
import MontserratSemiBold from './assets/fonts/MontserratSemiBold.ttf'
import MontserratThin from './assets/fonts/MontserratThin.ttf'
import { StatusBarController, ToastWrapper } from './src/components'
import Router from './src/screens/Router'

export default function App(): React.JSX.Element | null {
	const [fontsLoaded] = useFonts({
		<PERSON>serratT<PERSON>,
		<PERSON><PERSON>rat<PERSON><PERSON>,
		Montserrat,
		MontserratMedium,
		<PERSON><PERSON>rat<PERSON>emi<PERSON><PERSON>,
		Montser<PERSON><PERSON><PERSON>,
		MontserratExtraBold,
	})

	if (fontsLoaded) {
		return (
			<GestureHandlerRootView>
				<StatusBarController />
				<Router />
				<ToastWrapper />
			</GestureHandlerRootView>
		)
	}

	return null
}
