{"expo": {"name": "<PERSON><PERSON><PERSON> no <PERSON>", "slug": "olhonolance", "version": "2.30.2", "runtimeVersion": "2.30.2", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "dark", "owner": "oolhonolance", "splash": {"image": "./assets/splash.png", "resizeMode": "cover", "backgroundColor": "#000000"}, "updates": {"enabled": true, "checkAutomatically": "ON_LOAD", "fallbackToCacheTimeout": 0, "url": "https://u.expo.dev/1a46bd69-4125-43df-9904-0a9cfe97aa72"}, "assetBundlePatterns": ["**/*"], "ios": {"requireFullScreen": true, "supportsTablet": true, "bundleIdentifier": "com.htgabriel.olhonolance", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"compileSdkVersion": 34, "targetSdkVersion": 34, "minSdkVersion": 24, "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#1F1F1F"}, "permissions": ["android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_MEDIA_LOCATION"], "package": "com.htgabriel.olhonolance"}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "1a46bd69-4125-43df-9904-0a9cfe97aa72"}}, "androidStatusBar": {"backgroundColor": "#1F1F1F", "barStyle": "light-content"}, "plugins": ["expo-font", ["expo-av", {"microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone"}], ["expo-media-library", {"photosPermission": "Allow $(PRODUCT_NAME) to access your photos.", "savePhotosPermission": "Allow $(PRODUCT_NAME) to save photos.", "isAccessMediaLocationEnabled": true}], ["expo-screen-orientation", {"initialOrientation": "DEFAULT"}]]}}