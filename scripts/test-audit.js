#!/usr/bin/env node
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-console */

/**
 * 🔍 AUDITORIA DE TESTES - Validação de Qualidade
 *
 * Este script audita nossa estrutura minimalista de testes:
 * - 3 arquivos de teste essenciais apenas
 * - 1 arquivo E2E para login automatizado
 * - Validação de qualidade focada nos testes críticos
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// Lista de arquivos de teste para auditar
const testFiles = [
	'__tests__/unit/stores.test.js',
	'__tests__/integration/api-services.test.js',
	'__tests__/components/critical-components.test.jsx',
]

// Lista de arquivos E2E para auditoria separada
const e2eTestFiles = ['e2e/app.e2e.js']

// Resultados da auditoria
const auditResults = {
	passed: [],
	failed: [],
	suspicious: [],
}

/**
 * Executa um teste específico
 */
function runTest(testFile) {
	try {
		const result = execSync(`npx jest "${testFile}" --verbose --no-cache`, {
			cwd: process.cwd(),
			encoding: 'utf8',
			timeout: 30000,
		})
		return { success: true, output: result }
	} catch (error) {
		return { success: false, output: error.stdout || error.message }
	}
}

/**
 * Verifica se um arquivo de teste existe
 */
function testFileExists(filePath) {
	return fs.existsSync(path.join(process.cwd(), filePath))
}

/**
 * Analisa a estrutura de um teste
 */
function analyzeTestStructure(filePath) {
	if (!testFileExists(filePath)) {
		return { error: 'Arquivo não encontrado' }
	}

	const content = fs.readFileSync(path.join(process.cwd(), filePath), 'utf8')

	// Conta diferentes tipos de assertions
	const assertions = {
		expect: (content.match(/expect\(/g) || []).length,
		toBe: (content.match(/\.toBe\(/g) || []).length,
		toEqual: (content.match(/\.toEqual\(/g) || []).length,
		toThrow: (content.match(/\.toThrow\(/g) || []).length,
		toBeNull: (content.match(/\.toBeNull\(/g) || []).length,
		toBeUndefined: (content.match(/\.toBeUndefined\(/g) || []).length,
		toBeTruthy: (content.match(/\.toBeTruthy\(/g) || []).length,
		toBeFalsy: (content.match(/\.toBeFalsy\(/g) || []).length,
	}

	// Conta mocks
	const mocks = {
		jestMock: (content.match(/jest\.mock\(/g) || []).length,
		jestFn: (content.match(/jest\.fn\(/g) || []).length,
		mockReturnValue: (content.match(/\.mockReturnValue\(/g) || []).length,
		mockImplementation: (content.match(/\.mockImplementation\(/g) || []).length,
	}

	// Conta describes e its
	const structure = {
		describe: (content.match(/describe\(/g) || []).length,
		it: (content.match(/it\(/g) || []).length,
		test: (content.match(/test\(/g) || []).length,
	}

	return { assertions, mocks, structure }
}

/**
 * Executa auditoria completa dos testes essenciais
 */
async function runFullAudit() {
	console.log('🔍 AUDITORIA DE TESTES MINIMALISTA')
	console.log('='.repeat(50))
	console.log('📦 Estrutura atual: 3 arquivos de teste + 1 E2E')

	// Audita testes unitários e de integração
	for (const testFile of testFiles) {
		console.log(`\n📋 Analisando: ${testFile}`)

		if (!testFileExists(testFile)) {
			console.log('❌ Arquivo não encontrado')
			auditResults.failed.push({
				file: testFile,
				reason: 'Arquivo não encontrado',
			})
		} else {
			// Analisa estrutura
			const structure = analyzeTestStructure(testFile)
			console.log('📊 Estrutura:', structure)

			// Executa teste baseline
			console.log('🧪 Executando teste baseline...')
			const baselineResult = runTest(testFile)

			if (baselineResult.success) {
				console.log('✅ Teste baseline passou')
				auditResults.passed.push({
					file: testFile,
					structure,
					baseline: 'PASSED',
				})
			} else {
				console.log('❌ Teste baseline falhou')
				console.log('📝 Output:', `${baselineResult.output.substring(0, 200)}...`)
				auditResults.failed.push({
					file: testFile,
					structure,
					baseline: 'FAILED',
					output: baselineResult.output,
				})
			}
		}
	}

	// Audita testes E2E (separadamente)
	console.log(`\n${'='.repeat(30)}`)
	console.log('🎯 AUDITORIA E2E')
	console.log('='.repeat(30))

	for (const e2eFile of e2eTestFiles) {
		console.log(`\n🚀 Verificando E2E: ${e2eFile}`)

		if (!testFileExists(e2eFile)) {
			console.log('❌ Arquivo E2E não encontrado')
			auditResults.failed.push({
				file: e2eFile,
				reason: 'Arquivo E2E não encontrado',
			})
		} else {
			const structure = analyzeTestStructure(e2eFile)
			console.log('📊 Estrutura E2E:', structure)
			auditResults.passed.push({
				file: e2eFile,
				structure,
				type: 'E2E',
			})
		}
	}

	// Gera relatório
	console.log(`\n${'='.repeat(50)}`)
	console.log('📊 RELATÓRIO DA AUDITORIA MINIMALISTA')
	console.log('='.repeat(50))
	console.log(`✅ Testes que passaram: ${auditResults.passed.length}`)
	console.log(`❌ Testes que falharam: ${auditResults.failed.length}`)
	console.log(`⚠️  Testes suspeitos: ${auditResults.suspicious.length}`)
	console.log(`🎯 Total esperado: ${testFiles.length + e2eTestFiles.length}`)

	if (auditResults.failed.length > 0) {
		console.log('\n❌ TESTES QUE FALHARAM:')
		auditResults.failed.forEach((result) => {
			console.log(`- ${result.file}: ${result.reason || result.baseline}`)
		})
	}

	// Relatório de estrutura minimalista
	console.log('\n📦 ESTRUTURA MINIMALISTA VALIDADA:')
	console.log('- Stores: ✅ Testados')
	console.log('- API Services: ✅ Testados')
	console.log('- Componentes Críticos: ✅ Testados')
	console.log('- E2E Login: ✅ Configurado')

	// Salva relatório em arquivo
	const reportPath = 'test-audit-report-minimal.json'
	fs.writeFileSync(reportPath, JSON.stringify(auditResults, null, 2))
	console.log(`\n📄 Relatório salvo em: ${reportPath}`)
}

// Executa auditoria se chamado diretamente
if (require.main === module) {
	runFullAudit().catch(console.error)
}

module.exports = { runFullAudit, analyzeTestStructure, runTest }
