module.exports = {
	preset: 'react-native',
	testEnvironment: 'node',
	transformIgnorePatterns: [
		'node_modules/(?!(react-native|@react-native|react-native-.*|@react-navigation|expo|@expo|expo-linear-gradient|lottie-react-native|moti|zustand)/)',
	],
	collectCoverageFrom: [
		'src/**/*.{js,jsx,ts,tsx}',
		'!src/**/*.test.{js,jsx,ts,tsx}',
		'!src/**/index.js',
		'!src/**/*.styles.js',
		'!src/**/*.constants.js',
	],
	coverageDirectory: 'coverage',
	coverageReporters: ['text', 'lcov', 'html'],
	testMatch: [
		'<rootDir>/__tests__/**/*.test.{js,jsx,ts,tsx}',
		'<rootDir>/__tests__/**/*.spec.{js,jsx,ts,tsx}',
		'<rootDir>/src/**/tests/**/*.test.{js,jsx,ts,tsx}',
		'<rootDir>/src/**/tests/**/*.spec.{js,jsx,ts,tsx}',
	],
	moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx', 'json'],
	moduleDirectories: ['node_modules', '<rootDir>/src'],
	transform: {
		'^.+\\.(js|jsx)$': 'babel-jest',
		'^.+\\.(ts|tsx)$': 'babel-jest',
	},
}
