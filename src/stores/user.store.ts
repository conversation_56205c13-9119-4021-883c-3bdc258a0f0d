import { create } from 'zustand'
import type { User, UserComputed } from '../types'

interface UserStore {
  user: User | null
  setUser: (userData: User) => void
  clearUser: () => void
  updateUser: (updates: Partial<User>) => void
}

const useUserStore = create<UserStore>((set, get) => ({
	user: null,

	setUser: (userData: User) => set({ user: userData }),
	clearUser: () => set({ user: null }),
	updateUser: (updates: Partial<User>) => {
		const { user } = get()
		if (user) {
			set({ user: { ...user, ...updates } })
		}
	},
}))

// Função auxiliar para acessar computed properties
export const useUserComputed = (): UserComputed => {
	const { user, setUser, clearUser, updateUser } = useUserStore()
	return {
		user,
		setUser,
		clearUser,
		updateUser,
		signed: !!user,
	}
}

export default useUserStore
