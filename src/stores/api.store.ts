import { create } from 'zustand'
import { ErrorMessage } from '../helpers/HandleMessages'
import type { ApiConfig, ApiStore } from '../types'
import api from '../utils/api'

interface ApiState extends ApiStore {
	loading: boolean
	error: string | null
	isSectionLoading: (section: string) => boolean
	isAnySectionLoading: () => boolean
	setLoadingForSection: (section: string, isLoading: boolean) => void
}

const useApi = create<ApiState>((set, get) => ({
	loading: false,
	sectionLoading: {},
	error: null,

	isSectionLoading: (section: string) => get().sectionLoading[section] === true,

	// Verifica se alguma seção está carregando
	isAnySectionLoading: () => {
		const sections = get().sectionLoading
		return Object.values(sections).some((value) => value === true)
	},

	setLoadingForSection: (section: string, isLoading: boolean) => {
		set((state) => ({
			sectionLoading: { ...state.sectionLoading, [section]: isLoading },
		}))
	},

	callApi: async (
		{ url, method = 'GET', body = null, headers, config = {}, section = null, background = false }: ApiConfig,
		onSuccess?: (data: any) => void,
		onError?: (error: any) => void
	) => {
		if (!background) {
			if (section) {
				get().setLoadingForSection(section, true)
			} else {
				set({ loading: true })
			}
		}
		set({ error: null })

		try {
			// Log das requisições em modo de desenvolvimento
			// eslint-disable-next-line no-undef
			if (__DEV__) {
				const bodyLog = body ? ` - ${JSON.stringify(body)}` : ''
				// eslint-disable-next-line no-console
				console.log(`🌐 API Request: ${method} - ${url}${bodyLog}`)
			}

			let response: any

			switch (method) {
				case 'GET':
					response = await api.get(url, { ...config, headers })
					break
				case 'POST':
					response = await api.post(url, body, { ...config, headers })
					break
				case 'PUT':
					response = await api.put(url, body, { ...config, headers })
					break
				case 'DELETE':
					response = await api.delete(url, { ...config, headers })
					break
				default:
					throw new Error(`Método HTTP ${method} não suportado`)
			}

			// Log das respostas em modo de desenvolvimento
			// eslint-disable-next-line no-undef
			if (__DEV__) {
				// eslint-disable-next-line no-console
				// console.log(`✅ API Response: ${method} - ${url}`, response?.data || response)
			}

			if (onSuccess) {
				onSuccess(response?.data || response)
			}

			return response?.data || response
		} catch (error: any) {
			const errorMessage = error?.response?.data?.message || error?.message || 'Erro desconhecido'

			// Log dos erros em modo de desenvolvimento
			// eslint-disable-next-line no-undef
			if (__DEV__) {
				// eslint-disable-next-line no-console
				console.error(`❌ API Error: ${method} - ${url}`, error)
			}

			set({ error: errorMessage })

			if (onError) {
				onError(error)
			} else {
				ErrorMessage({ message: errorMessage })
			}

			throw error
		} finally {
			if (!background) {
				if (section) {
					get().setLoadingForSection(section, false)
				} else {
					set({ loading: false })
				}
			}
		}
	},

	resetApiData: () => {
		set({ loading: false, sectionLoading: {}, error: null })
	},
}))

export default useApi
