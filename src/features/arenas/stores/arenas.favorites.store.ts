import { create } from 'zustand'
import type { Arena } from '../../../types'
import { addArenaToFavorites, isArenaInFavorites, removeArenaFromFavorites } from '../utils/arenas.favorites.utils'

interface FavoriteArenasState {
	favoriteArenas: Arena[]
}

interface FavoriteArenasActions {
	setFavoriteArenas: (arenas: Arena[]) => void
	addFavoriteArena: (arena: Arena) => void
	removeFavoriteArena: (arenaId: string) => void
	isArenaFavorite: (arenaId: string) => boolean
	reset: () => void
}

type FavoriteArenasStore = FavoriteArenasState & FavoriteArenasActions

const initialState: FavoriteArenasState = {
	favoriteArenas: [],
}

/**
 * Store dedicada para gerenciar arenas favoritas do usuário
 */
const useFavoriteArenasStore = create<FavoriteArenasStore>((set, get) => ({
	...initialState,

	// Ações básicas de estado
	setFavoriteArenas: (arenas: Arena[]) => set({ favoriteArenas: arenas }),

	// Adicionar arena aos favoritos
	addFavoriteArena: (arena: Arena) => {
		const { favoriteArenas } = get()
		const newFavorites = addArenaToFavorites(favoriteArenas, arena)
		set({ favoriteArenas: newFavorites })
	},

	// Remover arena dos favoritos
	removeFavoriteArena: (arenaId: string) => {
		const { favoriteArenas } = get()
		const newFavorites = removeArenaFromFavorites(favoriteArenas, arenaId)
		set({ favoriteArenas: newFavorites })
	},

	// Verificar se uma arena é favorita
	isArenaFavorite: (arenaId: string): boolean => {
		const { favoriteArenas } = get()
		return isArenaInFavorites(favoriteArenas, arenaId)
	},

	// Resetar estado
	reset: () => {
		set(initialState)
	},
}))

export default useFavoriteArenasStore
