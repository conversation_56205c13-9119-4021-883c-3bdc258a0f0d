import { useCallback } from 'react'
import type { Arena } from '../../../types'
import { fetchFavoriteArenas, toggleFavoriteArena } from '../services/arenas.favorites.service'
import useFavoriteArenasStore from '../stores/arenas.favorites.store'
import { sortFavoriteArenas } from '../utils/arenas.favorites.utils'

/**
 * Hook para gerenciar arenas favoritas do usuário
 * Centraliza toda a lógica relacionada às arenas favoritas
 */
export const useFavoriteArenas = () => {
	const { favoriteArenas, setFavoriteArenas, addFavoriteArena, removeFavoriteArena, isArenaFavorite, reset } =
		useFavoriteArenasStore()

	/**
	 * Inicializa as arenas favoritas
	 */
	const initializeFavoriteArenas = useCallback(async (): Promise<void> => {
		reset()

		const favorites = await fetchFavoriteArenas()

		if (favorites) {
			const sortedFavorites = sortFavoriteArenas(favorites)
			setFavoriteArenas(sortedFavorites)
		}
	}, [reset, setFavoriteArenas])

	/**
	 * Alterna o status de favorito de uma arena
	 * @param arena - Objeto completo da arena (deve conter id_arena)
	 */
	const handleToggleFavorite = useCallback(async (arena: Arena): Promise<void> => {
		const isCurrentlyFavorite = isArenaFavorite(arena.id_arena)

		await toggleFavoriteArena(arena.id_arena)

		// Sucesso: atualiza o estado local
		if (isCurrentlyFavorite) {
			removeFavoriteArena(arena.id_arena)
		} else {
			addFavoriteArena(arena)
		}
	}, [isArenaFavorite, removeFavoriteArena, addFavoriteArena])

	return {
		// Estados
		favoriteArenas,

		// Ações principais
		initializeFavoriteArenas,
		handleToggleFavorite,

		// Reset
		reset,
	}
}
