import type { Arena } from '../../../types'

/**
 * Funções auxiliares puras para arenas favoritas
 * Responsabilidade: formatação, validação, ordenação
 */

/**
 * Ordena lista de arenas favoritas por nome
 * @param arenas - Lista de arenas para ordenar
 * @returns Lista ordenada de arenas
 */
export const sortFavoriteArenas = (arenas: Arena[]): Arena[] =>
	[...arenas].sort((a, b) => a.arena.localeCompare(b.arena))

/**
 * Verifica se uma arena já existe na lista de favoritos
 * @param favoriteArenas - Lista atual de favoritos
 * @param arenaId - ID da arena para verificar
 * @returns True se a arena já é favorita
 */
export const isArenaInFavorites = (favoriteArenas: Arena[], arenaId: string): boolean =>
	favoriteArenas.some((arena) => arena.id_arena === arenaId)

/**
 * Adiciona uma arena à lista de favoritos (sem duplicatas)
 * @param favoriteArenas - Lista atual de favoritos
 * @param newArena - Arena para adicionar
 * @returns Nova lista com a arena adicionada (ordenada)
 */
export const addArenaToFavorites = (favoriteArenas: Arena[], newArena: Arena): Arena[] => {
	const isAlreadyFavorite = isArenaInFavorites(favoriteArenas, newArena.id_arena)
	if (isAlreadyFavorite) return favoriteArenas

	const newFavorites = [...favoriteArenas, newArena]
	return sortFavoriteArenas(newFavorites)
}

/**
 * Remove uma arena da lista de favoritos
 * @param favoriteArenas - Lista atual de favoritos
 * @param arenaId - ID da arena para remover
 * @returns Nova lista sem a arena removida
 */
export const removeArenaFromFavorites = (favoriteArenas: Arena[], arenaId: string): Arena[] =>
	favoriteArenas.filter((arena) => arena.id_arena !== arenaId)
