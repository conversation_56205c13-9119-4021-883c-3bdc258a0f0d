import { useApi } from '../../../stores'
import type { Arena } from '../../../types'

const { callApi } = useApi.getState()

/**
 * <PERSON><PERSON> todas as arenas favoritas do usuário
 * @returns Retorna os dados das arenas favoritas
 */
export const fetchFavoriteArenas = async (): Promise<Arena[] | null> =>
	callApi({
		method: 'GET',
		url: '/favorite/arena',
		section: 'favoriteArenas',
	})

/**
 * Alterna o status de favorito de uma arena
 * @param arenaId - ID da arena
 * @returns Retorna o resultado da operação
 */
export const toggleFavoriteArena = async (arenaId: string): Promise<any> =>
	callApi({
		method: 'PUT',
		section: 'favoriteArenas',
		url: `/favorite/arena/${arenaId}`,
		background: true,
	})
