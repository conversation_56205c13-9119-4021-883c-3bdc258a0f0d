import { useEffect, useState } from 'react'
import { deleteLocalItem, getLocalItem, setLocalItem } from '../../helpers/AsyncStorage'
import { useUser } from '../../stores'
import useApi from '../../stores/api.store'
import type { User } from '../../types'
import api from '../../utils/api'
import { useFavoriteArenas } from '../arenas/hooks/arenas.favorites.hook'

export const useAutoLogin = () => {
	const { initializeFavoriteArenas } = useFavoriteArenas()
	const { setUser } = useUser()
	const { callApi } = useApi()

	const [isCheckingAuth, setIsCheckingAuth] = useState<boolean>(true)

	useEffect(() => {
		checkAutoLogin()
	}, [])

	const checkAutoLogin = async (): Promise<void> => {
		try {
			setIsCheckingAuth(true)

			const localUser: User | null = await getLocalItem('@user')

			if (!localUser?.token && !localUser?.access_token) {
				setIsCheckingAuth(false)
				return
			}

			const token = localUser.access_token || localUser.token
			api.defaults.headers.common.Authorization = `Bearer ${token}`

			const refreshData = await callApi({
				url: '/refresh_token',
				method: 'GET',
				background: true,
			})

			if (refreshData?.access_token) {
				const newToken = refreshData.access_token
				api.defaults.headers.common.Authorization = `Bearer ${newToken}`

				const updatedUserData: User = {
					...localUser,
					access_token: newToken,
					token: newToken,
				}

				await setLocalItem('@user', updatedUserData)
				setUser(updatedUserData)

				// Buscar arenas favoritas após auto-login bem-sucedido
				await initializeFavoriteArenas()
			} else {
				await deleteLocalItem('@user')
				delete api.defaults.headers.common.Authorization
			}

			setIsCheckingAuth(false)
		} catch (error) {
			await deleteLocalItem('@user')
			delete api.defaults.headers.common.Authorization
			setIsCheckingAuth(false)
		}
	}

	return {
		isCheckingAuth,
	}
}
