import { deleteLocalItem } from '../../helpers/AsyncStorage'
import { useBlockStore } from '../../screens/SignedRoutes/Clients/Block/core/block.store'
import { useReplayStore } from '../../screens/SignedRoutes/Replays/core/replays.store'

import useApi from '../../stores/api.store'
import useUser from '../../stores/user.store'
import api from '../../utils/api'
import useFavoriteArenasStore from '../arenas/stores/arenas.favorites.store'

export const useLogout = () => {
	const { clearUser } = useUser()
	const { reset } = useFavoriteArenasStore()
	const { resetReplayData } = useReplayStore()
	const { resetBlockData } = useBlockStore()
	const { resetApiData } = useApi()

	const logout = async (): Promise<void> => {
		try {
			reset()
			clearUser()

			await deleteLocalItem('@user')

			await Promise.all([
				new Promise<void>((resolve) => {
					resetReplayData()
					resolve()
				}),
				new Promise<void>((resolve) => {
					resetBlockData()
					resolve()
				}),
				new Promise<void>((resolve) => {
					resetApiData()
					resolve()
				}),
			])

			delete api.defaults.headers.common.Authorization
		} catch (error) {
			console.error('Erro durante logout:', error)
		}
	}

	return { logout }
}
