import AsyncStorage from '@react-native-async-storage/async-storage'
import type { StorageKey } from '../types'
import { ErrorMessage } from './HandleMessages'

/**
 * Remove um item do AsyncStorage
 * @param key - Chave do item a ser removido
 * @returns Promise<void>
 */
async function deleteLocalItem(key: StorageKey): Promise<void> {
	if (!key || typeof key !== 'string') {
		throw new Error('[AsyncStorage] Storage key must be a non-empty string')
	}

	try {
		return await AsyncStorage.removeItem(key)
	} catch (error) {
		ErrorMessage({ message: error?.toString() || 'Erro ao remover item do storage' })
		throw error
	}
}

/**
 * Salva um item no AsyncStorage
 * @param key - Chave para identificar o item
 * @param value - Valor a ser salvo (pode ser string ou objeto)
 * @returns Promise<boolean> - true se salvou com sucesso
 */
async function setLocalItem(key: StorageKey, value: any): Promise<boolean> {
	if (!key || typeof key !== 'string') {
		console.error('[AsyncStorage] Using undefined type for key is not supported. Key:', key, 'Type:', typeof key)
		throw new Error('[AsyncStorage] Storage key must be a non-empty string')
	}

	try {
		const valueToStore = typeof value === 'object' ? JSON.stringify(value) : String(value)
		await AsyncStorage.setItem(key, valueToStore)

		return true
	} catch (error) {
		ErrorMessage({ message: error?.toString() || 'Erro ao salvar item no storage' })
		return false
	}
}

/**
 * Recupera um item do AsyncStorage
 * @param key - Chave do item a ser recuperado
 * @returns Promise<any> - O valor recuperado ou null se não encontrado
 */
async function getLocalItem(key: StorageKey): Promise<any> {
	if (!key || typeof key !== 'string') {
		throw new Error('[AsyncStorage] Storage key must be a non-empty string')
	}

	try {
		const data = await AsyncStorage.getItem(key)

		if (data && typeof data === 'string') {
			try {
				return JSON.parse(data)
			} catch {
				// Se não conseguir parsear como JSON, retorna como string
				return data
			}
		}

		return null
	} catch (error) {
		ErrorMessage({ message: error?.toString() || 'Erro ao recuperar item do storage' })
		return null
	}
}

export { deleteLocalItem, getLocalItem, setLocalItem }
