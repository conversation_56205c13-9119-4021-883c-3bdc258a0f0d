import * as FileSystem from 'expo-file-system'
import * as MediaLibrary from 'expo-media-library'
import * as StoreReview from 'expo-store-review'
import Toast from 'react-native-toast-message'
import { ErrorMessage, SuccessMessage } from './HandleMessages'

interface DownloadProgressCallback {
	(isDownloading: boolean): void
}

interface ToastProgressParams {
	message: string
	progress: number
}

export const downloadFile = async (
	videoUrls: string | string[],
	setIsDownloading: DownloadProgressCallback = () => {}
): Promise<void> => {
	try {
		setIsDownloading(true)
		const urls = Array.isArray(videoUrls) ? videoUrls : [videoUrls]

		const { status } = await MediaLibrary.requestPermissionsAsync()
		if (status !== 'granted') {
			ErrorMessage({
				title: 'Permissão negada',
				message: 'Você precisa permitir o acesso à galeria para baixar os vídeos.',
			})
			return
		}

		const progressMap = new Array(urls.length).fill(0)

		const updateProgress = () => {
			const totalProgress = progressMap.reduce((sum, fileProgress) => sum + fileProgress, 0) / urls.length

			ToastProgress({
				message: urls.length > 1 ? 'Baixando vídeos' : 'Baixando vídeo',
				progress: totalProgress,
			})
		}

		const downloadVideo = async (videoUrl, index) => {
			const uniqueId = Date.now() + index
			const fileName = `OlhoNoLance-${uniqueId}.mp4`
			const fileUri = `${FileSystem.documentDirectory}${fileName}`

			const downloadResumable = FileSystem.createDownloadResumable(videoUrl, fileUri, {}, (downloadProgress) => {
				progressMap[index] =
					(downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite) * 100
				updateProgress()
			})

			const { uri } = await downloadResumable.downloadAsync()
			progressMap[index] = 100
			updateProgress()
			return uri
		}

		const downloadPromises = urls.map((url, index) => downloadVideo(url, index))
		const downloadedFiles = await Promise.all(downloadPromises)

		const savePromises = downloadedFiles.map((fileUri) => MediaLibrary.createAssetAsync(fileUri))
		await Promise.all(savePromises)

		if ((await StoreReview.hasAction()) && (await StoreReview.isAvailableAsync())) {
			await StoreReview.requestReview()
		}

		SuccessMessage({
			title: 'Download concluído',
			message: 'Os vídeos foram salvos na galeria do seu dispositivo.',
		})
	} catch (err) {
		console.error(err)
		ErrorMessage({
			title: 'Erro ao fazer download do vídeo: ',
			message: err.toString(),
		})
	} finally {
		setIsDownloading(false)
	}
}

const ToastProgress = ({ message, progress }: ToastProgressParams): void =>
	Toast.show({
		type: 'info',
		text1: progress ? `${Math.round(progress)}%` : '',
		text2: message,
		autoHide: false,
	})
