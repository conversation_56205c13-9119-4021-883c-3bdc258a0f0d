import { Popup } from 'react-native-popup-confirm-toast'
import Toast from 'react-native-toast-message'

const DURATION = 4000
const POSITION = 'bottom'

interface IconConfig {
	name: string
	library: string
}

interface MessageProps {
	type?: 'error' | 'info' | 'success'
	message: string
	title?: string
	messageIcon?: IconConfig
	titleIcon?: IconConfig
	position?: string
	duration?: number
	noTitleIcon?: boolean
}

type ConfirmCallback = () => void

const AskMessage = (
	title: string,
	message: string,
	onConfirm?: ConfirmCallback,
	onCancel?: ConfirmCallback,
	confirmText?: string,
	onlyConfirm: boolean = false
): void =>
	Popup.show({
		type: 'confirm',
		title,
		textBody: message,
		buttonText: confirmText || 'Sim',
		confirmText: 'Não',
		okButtonStyle: { backgroundColor: '#31A362' },
		confirmButtonStyle: { display: onlyConfirm ? 'none' : 'flex', backgroundColor: '#EA5252' },
		iconHeaderStyle: { backgroundColor: '#1F2120' },
		modalContainerStyle: { backgroundColor: '#1F2120' },
		confirmButtonTextStyle: { color: '#F9FAFB' },
		titleTextStyle: { color: '#F9FAFB' },
		descTextStyle: { color: '#F9FAFB' },
		okButtonTextStyle: { color: '#F9FAFB' },
		callback: () => {
			if (onConfirm) onConfirm()
			Popup.hide()
		},
		cancelCallback: () => {
			if (onCancel) onCancel()
			Popup.hide()
		},
	} as any)

const ShowMessage = ({
	type = 'info',
	message,
	title,
	messageIcon,
	titleIcon,
	position,
	duration,
	noTitleIcon = false,
}: MessageProps): void => {
	const iconConfig: Record<string, IconConfig> = {
		error: { name: 'x', library: 'Feather' },
		info: { name: 'info', library: 'Feather' },
		success: { name: 'check', library: 'Feather' },
	}

	Toast.show({
		type,
		text1: title || '',
		text2: message,
		position: (position as any) || POSITION,
		autoHide: true,
		visibilityTime: duration || DURATION,
	})
}

const ErrorMessage = (props: Omit<MessageProps, 'type'>): void => ShowMessage({ type: 'error', ...props })
const InfoMessage = (props: Omit<MessageProps, 'type'>): void => ShowMessage({ type: 'info', ...props })
const SuccessMessage = (props: Omit<MessageProps, 'type'>): void => ShowMessage({ type: 'success', ...props })

export { AskMessage, ErrorMessage, InfoMessage, SuccessMessage }
