import { create } from 'zustand'
import type { Arena } from '../../../types'

interface HeaderState {
	allArenas: Arena[]
	isSearchExpanded: boolean
	searchedArenas: Arena[]
	searchValue: string | null
}

interface HeaderActions {
	setAllArenas: (arenas: Arena[]) => void
	setIsSearchExpanded: (expanded: boolean) => void
	setSearchedArenas: (arenas: Arena[]) => void
	setSearchValue: (value: string | null) => void
	reset: () => void
}

type HeaderStore = HeaderState & HeaderActions

const initialState: HeaderState = {
	allArenas: [],
	isSearchExpanded: false,
	searchedArenas: [],
	searchValue: null,
}

const useHeaderStore = create<HeaderStore>((set, get) => ({
	...initialState,

	// Ações para atualizar estados (sem lógica de negócio)
	setAllArenas: (arenas: Arena[]) => set({ allArenas: arenas }),
	setIsSearchExpanded: (expanded: boolean) => set({ isSearchExpanded: expanded }),
	setSearchedArenas: (arenas: Arena[]) => set({ searchedArenas: arenas }),
	setSearchValue: (value: string | null) => set({ searchValue: value }),

	// Resetar estado
	reset: () => set(initialState),
}))

export { useHeaderStore }
