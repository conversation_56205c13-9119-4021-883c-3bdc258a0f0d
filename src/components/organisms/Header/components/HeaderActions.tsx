import React from 'react'
import { StyleSheet, View } from 'react-native'
import { useTheme } from '../../../../stores'
import type { Theme } from '../../../../types'
import { Icon } from '../../../atoms'

interface HeaderActionsProps {
	showBlock?: boolean
	showSearch?: boolean
	isSearchExpanded?: boolean
	toggleSearch?: () => void
	navigateToProfile?: () => void
	navigateToBlock?: () => void
}

const HeaderActions: React.FC<HeaderActionsProps> = ({
	showBlock,
	showSearch,
	isSearchExpanded,
	toggleSearch,
	navigateToProfile,
	navigateToBlock,
}) => {
	const { theme } = useTheme()
	const styles = createStyles(theme)

	return (
		<View style={styles.actionsContainer}>
			{showBlock && (
				<Icon
					library={'MaterialIcons'}
					name={'block'}
					size={'large'}
					color={theme.colors.red}
					onPress={navigateToBlock}
				/>
			)}

			{showSearch && (
				<Icon
					library={'Feather'}
					name={isSearchExpanded ? 'x' : 'search'}
					size={'large'}
					color={theme.colors.primary}
					onPress={toggleSearch}
				/>
			)}

			<Icon
				onPress={navigateToProfile}
				library={'Feather'}
				name={'user'}
				size={'large'}
				color={theme.colors.primary}
			/>
		</View>
	)
}

const createStyles = (theme: Theme) =>
	StyleSheet.create({
		actionsContainer: {
			gap: theme.spacing.s2,
			flexDirection: 'row',
		},
	})

export default React.memo(HeaderActions)
