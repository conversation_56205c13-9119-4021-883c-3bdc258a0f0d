import React, { forwardRef } from 'react'
import { StyleSheet, View } from 'react-native'
import { useTheme } from '../../../../stores'
import type { Theme } from '../../../../types'
import { TextInput } from '../../../molecules'

interface SearchInputProps {
	value?: string
	onChangeText?: (text: string) => void
	placeholder?: string
}

const SearchInput = forwardRef<any, SearchInputProps>(({ value, onChangeText, placeholder }, ref) => {
	const { theme } = useTheme()
	const styles = createStyles(theme)

	return (
		<View style={styles.searchInputContainer}>
			<TextInput
				ref={ref}
				value={value}
				onChangeText={onChangeText}
				placeholder={placeholder}
				containerStyle={styles.textInputContainer}
				style={styles.textInput}
			/>
		</View>
	)
})

const createStyles = (theme: Theme) =>
	StyleSheet.create({
		searchInputContainer: {
			flex: 1,
		},
		textInputContainer: {
			borderRadius: theme.spacing.s1,
			paddingHorizontal: theme.spacing.s1,
			backgroundColor: theme.colors.secondary,
			borderColor: 'transparent',
		},
		textInput: {
			height: 40,
		},
	})

SearchInput.displayName = 'SearchInput'

export default React.memo(SearchInput)
