import React from 'react'
import { StyleSheet } from 'react-native'
import { useTheme } from '../../../../stores'
import type { Theme } from '../../../../types'
import { Text } from '../../../atoms'

interface HeaderGreetingProps {
	title?: string
	firstName?: string
}

const HeaderGreeting: React.FC<HeaderGreetingProps> = ({ title, firstName }) => {
	const { theme } = useTheme()
	const styles = createStyles(theme)

	return (
		<Text style={styles.greeting}>
			{title || (
				<>
					Olá, <Text style={styles.userName}>{firstName}</Text>
				</>
			)}
		</Text>
	)
}

const createStyles = (theme: Theme) =>
	StyleSheet.create({
		greeting: {
			fontSize: theme.fontSize.large,
			color: theme.colors.text,
			fontWeight: 'bold',
		},
		userName: {
			color: theme.colors.primary,
			fontWeight: 'bold',
		},
	})

export default React.memo(HeaderGreeting)
