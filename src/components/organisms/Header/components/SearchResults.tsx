import React from 'react'
import { StyleSheet, TouchableOpacity, View } from 'react-native'
import Image from 'react-native-fast-image'
import { FlatList } from 'react-native-gesture-handler'
import { useTheme } from '../../../../stores'
import type { Theme } from '../../../../types'
import { Icon, Text } from '../../../atoms'

interface Arena {
	id_arena: number | string
	arena: string
	[key: string]: any
}

interface SearchResultsProps {
	searchedArenas: Arena[]
	favoriteArenas?: Arena[]
	handleSelectArena?: (arena: Arena) => void
	toggleFavoriteArena?: (arena: Arena) => void
	top?: number
}

interface ResultItemProps {
	item: Arena
	index: number
	theme: Theme
	favoriteArenas?: Arena[]
	handleSelectArena?: (arena: Arena) => void
	toggleFavoriteArena?: (arena: Arena) => void
}

const SearchResults: React.FC<SearchResultsProps> = ({
	searchedArenas,
	favoriteArenas,
	handleSelectArena,
	toggleFavoriteArena,
	top,
}) => {
	const { theme } = useTheme()
	const styles = createStyles(theme, top)

	if (searchedArenas.length === 0) return null

	return (
		<FlatList
			style={styles.dropdown}
			data={searchedArenas}
			showsVerticalScrollIndicator={false}
			keyExtractor={(item) => `search-${item.id_arena}`}
			keyboardShouldPersistTaps={'handled'}
			contentContainerStyle={{ padding: theme.spacing.s1 }}
			renderItem={({ item, index }) => (
				<ResultItem
					item={item}
					index={index}
					theme={theme}
					favoriteArenas={favoriteArenas}
					handleSelectArena={handleSelectArena}
					toggleFavoriteArena={toggleFavoriteArena}
				/>
			)}
		/>
	)
}

const ResultItem: React.FC<ResultItemProps> = React.memo(
	({ item, index, theme, favoriteArenas, handleSelectArena, toggleFavoriteArena }) => {
		const styles = createStyles(theme)
		const isFavorite = favoriteArenas?.find((a) => a.id_arena === item.id_arena)

		return (
			<TouchableOpacity
				onPress={() => handleSelectArena?.(item)}
				style={StyleSheet.compose(
					styles.resultItem,
					index % 2 === 1 && { backgroundColor: theme.colors.secondary }
				)}
			>
				<View style={styles.searchedArenasTextContainer}>
					<Image
						source={{
							uri: `https://api.oolhonolance.com.br/img/arenas/${item.id_arena}.png?hash=true`,
							priority: Image.priority.high,
						}}
						style={{ width: 30, height: 30 }}
					/>

					<Text style={styles.searchedArenasText} numberOfLines={1}>
						{item.arena}
					</Text>
				</View>

				<TouchableOpacity style={styles.btnSetFavorite} onPress={() => toggleFavoriteArena?.(item)}>
					<Icon
						name={isFavorite ? 'star' : 'star-outline'}
						library={'MaterialIcons'}
						size={'medium'}
						color={theme.colors.yellow}
					/>
				</TouchableOpacity>
			</TouchableOpacity>
		)
	}
)

const createStyles = (theme: Theme, top?: number) =>
	StyleSheet.create({
		dropdown: {
			position: 'absolute',
			top: top ? top + 50 : 50,
			left: 0,
			right: 0,
			marginHorizontal: theme.spacing.s1,
			backgroundColor: theme.colors.background,
			borderWidth: 1,
			borderColor: theme.colors.tertiary,
			borderRadius: theme.spacing.s1,
			maxHeight: 300,
			zIndex: 999,
		},
		resultItem: {
			flexDirection: 'row',
			alignItems: 'center',
			justifyContent: 'space-between',
			gap: theme.spacing.s1,
			paddingHorizontal: theme.spacing.s1,
			paddingVertical: theme.spacing.s1,
			borderRadius: theme.spacing.s1,
		},
		searchedArenasTextContainer: {
			flex: 1,
			flexDirection: 'row',
			alignItems: 'center',
			gap: theme.spacing.s1,
		},
		searchedArenasText: {
			flex: 1,
		},
		btnSetFavorite: {
			padding: theme.spacing.s1,
		},
	})

ResultItem.displayName = 'ResultItem'

export default React.memo(SearchResults)
