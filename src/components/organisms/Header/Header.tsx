import React from 'react'
import { StyleSheet, View } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

import { useFavoriteArenas } from '../../../features'
import { useTheme, useUser } from '../../../stores'
import type { Theme } from '../../../types'
import { useHeader } from './header.hook'

import HeaderActions from './components/HeaderActions'
import HeaderGreeting from './components/HeaderGreeting'
import SearchInput from './components/SearchInput'
import SearchResults from './components/SearchResults'

interface HeaderProps {
	title?: string
	showSearch?: boolean
	showBlock?: boolean
}

interface SafeAreaInsets {
	top: number
}

const Header: React.FC<HeaderProps> = ({ title, showSearch = false, showBlock = false }) => {
	const {
		isSearchExpanded,
		searchedArenas,
		searchValue,
		searchInputRef,
		handleToggleSearch,
		handleSearch,
		handleSelectArena,
		navigateToProfile,
		navigateToBlock,
	} = useHeader()

	const { favoriteArenas, handleToggleFavorite } = useFavoriteArenas()
	const { top } = useSafeAreaInsets()
	const { theme } = useTheme()
	const { user } = useUser()

	const firstName = user.name ? user.name.split(' ')[0] : 'Usuário'
	const styles = createStyles(theme, { top })

	return (
		<View style={styles.headerContainer}>
			<View style={styles.container}>
				{!isSearchExpanded && <HeaderGreeting title={title} firstName={firstName} />}

				{isSearchExpanded && (
					<SearchInput
						ref={searchInputRef}
						value={searchValue}
						onChangeText={handleSearch}
						placeholder={'Digite o nome de uma cidade ou arena'}
					/>
				)}

				<HeaderActions
					showBlock={showBlock}
					showSearch={showSearch}
					isSearchExpanded={isSearchExpanded}
					toggleSearch={handleToggleSearch}
					navigateToProfile={navigateToProfile}
					navigateToBlock={navigateToBlock}
				/>
			</View>

			<SearchResults
				searchedArenas={searchedArenas}
				favoriteArenas={favoriteArenas}
				handleSelectArena={handleSelectArena}
				toggleFavoriteArena={handleToggleFavorite}
				top={top}
			/>
		</View>
	)
}

const createStyles = (theme: Theme, insets: SafeAreaInsets) =>
	StyleSheet.create({
		headerContainer: {
			zIndex: 2,
		},
		container: {
			paddingTop: insets.top,
			height: insets.top + 50,
			gap: theme.spacing.s1,
			paddingHorizontal: theme.spacing.s1,
			paddingVertical: theme.spacing.s2,
			justifyContent: 'space-between',
			flexDirection: 'row',
			alignItems: 'center',
		},
	})

export default Header
