/**
 * Utilitários para o Header
 * Funções auxiliares puras: formatação, validação, ordenação
 */

import type { Arena } from '../../../types'

interface SearchState {
	isSearchExpanded?: boolean
	searchValue?: string | null
	searchedArenas?: Arena[]
}

interface Setters {
	setIsSearchExpanded: (value: boolean) => void
	setSearchValue: (value: string | null) => void
	setSearchedArenas: (arenas: Arena[]) => void
}

/**
 * Remove acentos de um texto para facilitar busca
 * @param text - Texto a ser normalizado
 * @returns Texto sem acentos
 */
export const removeAccents = (text: string): string => {
	if (!text || typeof text !== 'string') return ''
	return text.normalize('NFD').replace(/[\u0300-\u036f]/g, '')
}

/**
 * Filtra arenas por nome ou cidade
 * @param arenas - Array de arenas
 * @param searchValue - Valor de busca
 * @returns Arenas filtradas
 */
export const filterArenasBySearch = (arenas: Arena[], searchValue: string): Arena[] => {
	if (!Array.isArray(arenas) || !searchValue || searchValue.length < 2) {
		return []
	}

	const normalizedSearch = removeAccents(searchValue.toLowerCase())

	return arenas.filter((arena) => {
		const normalizedArenaName = removeAccents((arena.arena || '').toLowerCase())
		const normalizedCity = removeAccents((arena.cidade || '').toLowerCase())

		return normalizedArenaName.includes(normalizedSearch) || normalizedCity.includes(normalizedSearch)
	})
}

/**
 * Lógica para alternar estado de busca expandida
 * @param isCurrentlyExpanded - Estado atual da busca
 * @returns Novos valores para os estados
 */
export const toggleSearchState = (isCurrentlyExpanded: boolean): SearchState => ({
	isSearchExpanded: !isCurrentlyExpanded,
	...(isCurrentlyExpanded && {
		searchValue: null,
		searchedArenas: [],
	}),
})

/**
 * Estado limpo para quando busca é fechada
 * @returns Estados resetados para busca
 */
export const getCleanSearchState = (): SearchState => ({
	searchValue: '',
	searchedArenas: [],
	isSearchExpanded: false,
})

/**
 * Aplica múltiplos estados usando os setters da store
 * @param newState - Novos estados para aplicar
 * @param setters - Funções setters da store
 */
export const applyMultipleStates = (newState: SearchState, setters: Setters): void => {
	const { setIsSearchExpanded, setSearchValue, setSearchedArenas } = setters

	if (newState.isSearchExpanded !== undefined) {
		setIsSearchExpanded(newState.isSearchExpanded)
	}
	if (newState.searchValue !== undefined) {
		setSearchValue(newState.searchValue)
	}
	if (newState.searchedArenas !== undefined) {
		setSearchedArenas(newState.searchedArenas)
	}
}

/**
 * Determina quais arenas devem ser exibidas na busca
 * @param searchValue - Valor atual da busca
 * @param allArenas - Todas as arenas disponíveis
 * @returns Arenas filtradas ou array vazio
 */
export const getFilteredArenas = (searchValue: string, allArenas: Arena[]): Arena[] => {
	if (searchValue && searchValue.length >= 2 && allArenas?.length > 0) {
		return filterArenasBySearch(allArenas, searchValue)
	}
	return []
}
