import { useNavigation } from '@react-navigation/native'
import { useCallback, useEffect, useRef } from 'react'
import { TextInput } from 'react-native'

import { useReplayFilters } from '../../../screens/SignedRoutes/Replays/core/replays.filters.hook'
import type { Arena } from '../../../types'
import { fetchAllArenas } from './header.service'
import { useHeaderStore } from './header.store'
import { applyMultipleStates, getCleanSearchState, getFilteredArenas, toggleSearchState } from './header.utils'

interface UseHeaderReturn {
	// Estados
	allArenas: Arena[]
	isSearchExpanded: boolean
	searchedArenas: Arena[]
	searchValue: string

	// Refs
	searchInputRef: React.RefObject<TextInput>

	// Ações
	initializeHeaderData: () => Promise<void>
	handleToggleSearch: () => void
	handleSearch: (text: string) => void
	handleSelectArena: (item: Arena) => void
	navigateToProfile: () => void
	navigateToBlock: () => void
}

/**
 * Hook específico para o Header - sem dependências de replays
 * Responsável apenas pelo gerenciamento de busca de arenas
 */
export const useHeader = (onArenaSelect?: (arenaId: string) => void): UseHeaderReturn => {
	const {
		allArenas,
		isSearchExpanded,
		searchedArenas,
		searchValue,
		setAllArenas,
		setIsSearchExpanded,
		setSearchedArenas,
		setSearchValue,
		reset,
	} = useHeaderStore()

	const { updateSelectedArena } = useReplayFilters()

	const navigation = useNavigation()
	const searchInputRef = useRef<TextInput>(null)

	useEffect(() => {
		initializeHeaderData()
	}, [])

	// Filtra arenas quando searchValue ou allArenas mudam
	useEffect(() => {
		const filteredArenas = getFilteredArenas(searchValue, allArenas)
		setSearchedArenas(filteredArenas)
	}, [searchValue, allArenas, setSearchedArenas])

	// Inicializa os dados do header
	const initializeHeaderData = async (): Promise<void> => {
		reset()

		const allArenasData = await fetchAllArenas()
		if (allArenasData) setAllArenas(allArenasData)
	}

	// Toggle do search com foco
	const handleToggleSearch = useCallback(() => {
		const newState = toggleSearchState(isSearchExpanded)
		const setters = { setIsSearchExpanded, setSearchValue, setSearchedArenas }

		applyMultipleStates(newState, setters)

		if (!isSearchExpanded) {
			setTimeout(() => searchInputRef.current?.focus(), 0)
		}
	}, [isSearchExpanded, setIsSearchExpanded, setSearchValue, setSearchedArenas])

	// Handler para mudança no texto de busca
	const handleSearch = useCallback(
		(text: string) => {
			setSearchValue(text)
		},
		[setSearchValue]
	)

	// Handler para seleção de arena
	const handleSelectArena = useCallback(
		(item: Arena) => {
			updateSelectedArena(item.id_arena)

			const cleanState = getCleanSearchState()
			const setters = { setIsSearchExpanded, setSearchValue, setSearchedArenas }

			applyMultipleStates(cleanState, setters)
		},
		[updateSelectedArena, setIsSearchExpanded, setSearchValue, setSearchedArenas]
	)

	// Navegação para perfil
	const navigateToProfile = useCallback(() => {
		navigation.navigate('Profile' as never)
	}, [navigation])

	// Navegação para block
	const navigateToBlock = useCallback(() => {
		navigation.navigate('Block' as never)
	}, [navigation])

	return {
		// Estados
		allArenas,
		isSearchExpanded,
		searchedArenas,
		searchValue,

		// Refs
		searchInputRef,

		// Ações
		initializeHeaderData,
		handleToggleSearch,
		handleSearch,
		handleSelectArena,
		navigateToProfile,
		navigateToBlock,
	}
}
