import { useEffect, useState } from 'react'
import { StyleSheet, View } from 'react-native'
import Mod from 'react-native-modal'

import { useTheme } from '../../../stores'
import { ModalProps, Theme } from '../../../types'
import { Button } from '../../atoms'

export default function Modal({ visible = false, style = {}, onDismiss, children, ...args }: ModalProps) {
	const [isVisible, setIsVisible] = useState(visible)
	const { theme } = useTheme()
	const styles = createStyle(theme)

	useEffect(() => {
		setIsVisible(visible)
	}, [visible])

	return (
		<Mod useNativeDriver={true} isVisible={isVisible} {...args}>
			<View style={StyleSheet.compose(styles.container, style)}>
				<Button
					type={'text'}
					style={styles.closeButton}
					onPress={() => {
						setIsVisible(!isVisible)
						if (onDismiss) onDismiss()
					}}
					icon={{
						name: 'close-a',
						library: 'Fontisto',
						size: 'small',
						color: 'white',
					}}
				/>

				{children}
			</View>
		</Mod>
	)
}

const createStyle = (theme: Theme) =>
	StyleSheet.create({
		container: {
			padding: theme.spacing.s2,
			backgroundColor: theme.colors.secondary,
			borderRadius: theme.spacing.s2,
			gap: theme.spacing.s2,
		},
		closeButton: {
			alignSelf: 'flex-end',
			position: 'absolute',
			top: 0,
			right: 0,
			paddingHorizontal: theme.spacing.s2,
			paddingVertical: theme.spacing.s2,
			zIndex: 1,
		},
	})
