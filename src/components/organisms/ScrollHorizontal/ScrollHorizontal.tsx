import { Skeleton } from 'moti/skeleton'
import { useRef } from 'react'
import { FlatList, StyleSheet, View } from 'react-native'

import { useTheme } from '../../../stores'
import { ScrollHorizontalProps, Theme } from '../../../types'

import { Item } from '../../molecules'

const ScrollHorizontal = ({
	items,
	active,
	idxName,
	idxItem,
	onPress,
	idxImage,
	loading = false,
	onPressIcone,
	imageStyle,
	style,
	icon,
	favoriteItems = [], // Lista de itens favoritos para comparar
	favoriteIdxItem, // Campo para identificar favoritos
}: ScrollHorizontalProps) => {
	const { theme } = useTheme()

	const scrollRef = useRef<FlatList>(null)
	const styles = createStyle(theme)

	const handleItemPress = (item: any, index: number) => {
		if (scrollRef.current) {
			scrollRef.current.scrollToIndex({
				index,
				viewPosition: 0.5,
				animated: true,
			})
		}

		if (onPress) onPress(item[idxItem])
	}

	const getSkeletonWidth = () => {
		const randomWidth = Math.floor(Math.random() * 9 + 8) * 10
		return randomWidth
	}

	if (loading) {
		return (
			<View style={styles.skeletonContainer}>
				{[1, 2, 3, 4, 5].map((item) => (
					<Skeleton
						key={`skeleton-${item}`}
						width={getSkeletonWidth()}
						height={style?.height || 30}
						radius={theme.spacing.s1}
						colorMode={'dark'}
						colors={[theme.colors.secondary, theme.colors.textLight]}
					/>
				))}
			</View>
		)
	}

	return (
		<FlatList
			ref={scrollRef}
			horizontal={true}
			showsHorizontalScrollIndicator={false}
			contentContainerStyle={styles.favorites}
			data={items}
			keyExtractor={(item, index) => `${idxItem}-${item[idxItem]}-${index}`}
			renderItem={({ item, index }) => {
				// Determinar se o item é favorito
				const isFavorite = favoriteItems.some(
					(favItem) => favoriteIdxItem && favItem[favoriteIdxItem] === item[favoriteIdxItem]
				)

				// Modificar o ícone baseado se é favorito ou não
				const modifiedIcon = icon
					? {
							...icon,
							name: isFavorite ? 'star' : 'star-outline',
						}
					: null

				return (
					<Item
						name={item[idxName]}
						onPress={() => handleItemPress(item, index)}
						active={active === item[idxItem]}
						image={idxImage ? item[idxImage] : null}
						onPressIcon={() => (onPressIcone ? onPressIcone(item) : {})}
						imageStyle={imageStyle}
						style={style}
						icon={modifiedIcon}
					/>
				)
			}}
		/>
	)
}

const createStyle = (theme: Theme) =>
	StyleSheet.create({
		favorites: {
			gap: theme.spacing.s1,
		},
		skeletonContainer: {
			flexDirection: 'row',
			gap: theme.spacing.s1,
		},
	})

export default ScrollHorizontal
