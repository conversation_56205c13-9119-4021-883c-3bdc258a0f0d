import { Root as PopupRootProvider } from 'react-native-popup-confirm-toast'
import Toast, { ToastConfig } from 'react-native-toast-message'
import ToastMessages from './ToastMessages'

const toastConfig: ToastConfig = {
	error: (props: any) => <ToastMessages type={'error'} {...props} />,
	info: (props: any) => <ToastMessages type={'info'} {...props} />,
	success: (props: any) => <ToastMessages type={'success'} {...props} />,
}

const ToastWrapper = () => (
	<>
		<PopupRootProvider />
		<Toast config={toastConfig} />
	</>
)

export default ToastWrapper
