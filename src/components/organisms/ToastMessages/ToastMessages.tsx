import { SafeAreaView, View } from 'react-native'
import { useTheme } from '../../../stores'
import { Theme, ToastMessagesProps } from '../../../types'
import { Icon, Text } from '../../atoms'

const getTypeColor = (theme: Theme, type: ToastMessagesProps['type']) => {
	const typeColors = {
		error: theme.colors.red,
		success: theme.colors.primary,
		info: theme.colors.blue,
	}
	return typeColors[type] || theme.colors.red
}

const ToastMessages = ({ type, ...props }: ToastMessagesProps) => {
	const { theme } = useTheme()
	const styles = createStyle(theme, type)
	const typeColor = getTypeColor(theme, type)

	return (
		<SafeAreaView>
			<View style={styles.messageContainer} testID={`toast-${type}`}>
				{(props.text1?.text || props.text1?.icon) && (
					<View style={styles.textRow}>
						{props.text1?.icon?.name && (
							<Icon
								color={typeColor}
								library={(props.text1.icon.library as any) || 'Feather'}
								name={props.text1.icon.name}
								size={(props.text1.icon.size as any) || 'medium'}
							/>
						)}
						{props.text1?.text && (
							<Text size={'medium'} style={{ fontWeight: '600' }}>
								{props.text1.text}
							</Text>
						)}
					</View>
				)}

				{(props.text2?.text || props.text2?.icon) && (
					<View style={styles.textRow}>
						{props.text2?.icon?.name && (
							<Icon
								library={(props.text2.icon.library as any) || 'Feather'}
								name={props.text2.icon.name}
								size={(props.text2.icon.size as any) || 'medium'}
								color={props.text2.icon.color}
							/>
						)}
						{props.text2?.text && (
							<Text style={styles.secondaryText} color={theme.colors.textBody}>
								{props.text2.text}
							</Text>
						)}
					</View>
				)}
			</View>
		</SafeAreaView>
	)
}

const createStyle = (theme: Theme, type: ToastMessagesProps['type']) => ({
	container: {
		flex: 1,
	},
	messageContainer: {
		paddingHorizontal: theme.spacing.s4,
		paddingVertical: theme.spacing.s2,
		backgroundColor: theme.colors.base100,
		alignItems: 'center' as const,
		justifyContent: 'center' as const,
		borderRadius: theme.spacing.s10,
		borderWidth: 1,
		borderColor: getTypeColor(theme, type),
		gap: theme.spacing.s1,
	},
	textRow: {
		flexDirection: 'row' as const,
		gap: theme.spacing.s1,
		alignItems: 'center' as const,
	},
	secondaryText: {
		textAlign: 'center' as const,
		fontSize: 13,
	},
})

export default ToastMessages
