import { StyleSheet, TouchableOpacity, View } from 'react-native'

import { useTheme } from '../../../stores'
import { BoxProps, Theme } from '../../../types'
import { Text } from '../../atoms'

export default function Box({ id, label, onPress, value, ...args }: BoxProps) {
	const { theme } = useTheme()
	const styles = createStyle(theme)
	const CustomComponent = onPress ? TouchableOpacity : View

	return (
		<CustomComponent onPress={() => onPress && onPress(id)} style={styles.box} {...args}>
			<Text size={'large'} style={styles.label}>
				{label}
			</Text>
			<Text size={'extraLarge'} style={styles.value}>
				{value}
			</Text>
		</CustomComponent>
	)
}

const createStyle = (theme: Theme) =>
	StyleSheet.create({
		box: {
			paddingVertical: theme.spacing.s2,
			paddingHorizontal: theme.spacing.s4,
			backgroundColor: theme.colors.secondary,
			borderRadius: theme.spacing.s1,
			alignItems: 'center',
			justifyContent: 'center',
			gap: theme.spacing.s2,
		},
		label: {
			fontWeight: '800',
			textAlign: 'center',
		},
		value: {
			color: theme.colors.primary,
			fontWeight: '800',
		},
	})
