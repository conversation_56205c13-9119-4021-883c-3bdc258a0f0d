import { useEffect, useState } from 'react'
import { StyleSheet, TouchableOpacity, View } from 'react-native'

import { useTheme } from '../../../stores'
import { CheckProps } from '../../../types'
import Icon from '../../atoms/Icon'
import Text from '../../atoms/Text'

export default function Check({ checked = false, label, onPress = () => {}, style, ...args }: CheckProps) {
	const [check, setCheck] = useState(checked || false)
	const { theme } = useTheme()

	useEffect(() => {
		setCheck(checked)
	}, [checked])

	const _handlePress = () => {
		setCheck(!check)
		onPress(!check)
	}

	const styles = StyleSheet.create({
		btn: {
			alignItems: 'center',
			flexDirection: 'row',
		},
		container: {
			width: 20,
			height: 20,
			alignItems: 'center',
			justifyContent: 'center',
			borderColor: theme.colors.tertiary,
			borderWidth: 1,
			marginRight: theme.spacing.s1,
			borderRadius: theme.spacing.s1 / 2,
		},
		label: {
			fontSize: theme.fontSize.small,
			color: theme.colors.textLight,
		},
	})

	return (
		<TouchableOpacity onPress={_handlePress} style={StyleSheet.compose(styles.btn, style)} {...args}>
			<View style={styles.container}>
				{check && (
					<Icon library="Feather" name={'check'} size={theme.fontSize.medium} color={theme.colors.primary} />
				)}
			</View>

			{label && <Text style={styles.label}>{label}</Text>}
		</TouchableOpacity>
	)
}
