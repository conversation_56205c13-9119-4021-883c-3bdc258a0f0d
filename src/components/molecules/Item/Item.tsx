import { Animated, Pressable, StyleSheet, View } from 'react-native'
import { LinearGradient } from 'expo-linear-gradient'
import { useRef } from 'react'

import Image from 'react-native-fast-image'

import { useTheme } from '../../../stores'
import { Icon, Text } from '../../atoms'
import { ItemProps, Theme } from '../../../types'

export default function Item({
	active,
	name,
	image,
	icon,
	onPressIcon,
	imageStyle,
	video,
	style,
	containerStyle,
	aspectRatio,
	onPress,
	isSelectionMode,
	...args
}: ItemProps) {
	const { theme } = useTheme()

	const scaleAnim = useRef(new Animated.Value(1)).current
	const styles = createStyle(theme, aspectRatio)

	const borderGradientStyle = {
		...styles.borderGradient,
		...(active ? { padding: 2 } : { padding: 0 }),
	}

	return (
		<LinearGradient
			colors={
				active ? [theme.colors.blue, theme.colors.primary] : [theme.colors.secondary, theme.colors.secondary]
			}
			start={[0, 0]}
			end={[1, 0]}
			style={StyleSheet.compose(borderGradientStyle, containerStyle)}
		>
			<Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
				<Pressable
					style={StyleSheet.compose(image ? styles.container : styles.containerNoImage, style)}
					onPress={active && !isSelectionMode ? undefined : onPress}
					{...args}
				>
					{icon && (
						<Pressable style={icon.style} onPress={onPressIcon}>
							<Icon
								library={icon.library as any}
								name={icon.name}
								size={icon.size || 'medium'}
								color={icon.color || theme.colors.textLight}
								style={icon.style}
							/>
						</Pressable>
					)}

					{image && (
						<View style={styles.mediaContainer}>
							<Image
								style={StyleSheet.compose(styles.image, imageStyle)}
								source={{
									uri:
										image.indexOf('http') !== -1
											? image
											: `https://api.oolhonolance.com.br/img/arenas/${image}.png`,
									priority: Image.priority.high,
								}}
								resizeMode={Image.resizeMode.contain}
							/>
						</View>
					)}

					<Text style={styles.name} numberOfLines={1}>
						{name}
					</Text>
				</Pressable>
			</Animated.View>
		</LinearGradient>
	)
}

const createStyle = (theme: Theme, aspectRatio?: number) =>
	StyleSheet.create({
		container: {
			width: '100%',
			alignItems: 'center',
			padding: 0,
			borderRadius: theme.spacing.s1,
			backgroundColor: theme.colors.secondary,
			overflow: 'hidden',
		},
		containerNoImage: {
			paddingHorizontal: theme.spacing.s2,
			borderRadius: theme.spacing.s1,
			backgroundColor: theme.colors.secondary,
		},
		mediaContainer: {
			alignItems: 'center',
			justifyContent: 'center',
			aspectRatio: aspectRatio || 16 / 9,
			borderTopStartRadius: theme.spacing.s1,
			borderTopEndRadius: theme.spacing.s1,
			overflow: 'hidden',
		},
		image: {
			width: 50,
			height: 50,
		},
		name: {
			fontWeight: 'semibold',
			textAlign: 'center',
			paddingVertical: theme.spacing.s1,
		},
		borderGradient: {
			justifyContent: 'center',
			borderRadius: theme.spacing.s1,
		},
	})
