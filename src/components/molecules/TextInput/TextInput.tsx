import { forwardRef } from 'react'
import { TextInput as Input, StyleSheet, View } from 'react-native'
import { TextInputMask } from 'react-native-masked-text'

import { useTheme } from '../../../stores'
import { TextInputProps, Theme } from '../../../types'
import { Icon, Text } from '../../atoms'

const TextInput = forwardRef<Input, TextInputProps>(
	(
		{
			name,
			icon,
			iconPosition = 'left',
			label,
			value,
			style,
			placeholder,
			containerStyle,
			secureTextEntry = false,
			mask,
			error,
			onChangeText = () => {},
			...args
		},
		ref
	) => {
		const { theme } = useTheme()
		const styles = createStyle(theme, iconPosition, error)

		return (
			<View>
				{label && <Text style={styles.label}>{label}</Text>}

				<View style={StyleSheet.compose(styles.container, containerStyle)}>
					{icon && (
						<Icon
							library={icon?.library as any}
							name={icon?.name}
							size={icon?.size}
							color={icon?.color || theme.colors?.secondary}
							style={StyleSheet.compose(styles.icon, icon.style)}
							onPress={icon?.onPress}
						/>
					)}

					{mask === 'whatsapp' ? (
						<TextInputMask
							style={StyleSheet.compose(styles.input, style)}
							onChangeText={(formatted) => {
								const rawValue = formatted.replace(/\D/g, '')
								onChangeText?.(formatted, rawValue)
							}}
							placeholderTextColor={theme.colors.tertiary}
							secureTextEntry={secureTextEntry}
							options={{ mask: '(99) 99999-9999' }}
							underlineColorAndroid="transparent"
							placeholder={placeholder}
							type={'custom'}
							value={value}
							{...args}
						/>
					) : (
						<Input
							ref={ref}
							style={StyleSheet.compose(styles.input, style)}
							secureTextEntry={secureTextEntry}
							underlineColorAndroid="transparent"
							placeholder={placeholder}
							placeholderTextColor={theme.colors.tertiary}
							value={value}
							onChangeText={(v) => onChangeText?.(v)}
							{...args}
						/>
					)}
				</View>
				{error && <Text style={styles.errorText}>{error}</Text>}
			</View>
		)
	}
)

const createStyle = (theme: Theme, iconPosition?: string, hasError?: string) =>
	StyleSheet.create({
		container: {
			height: 50,
			flexDirection: iconPosition === 'left' ? 'row' : 'row-reverse',
			alignItems: 'center',
			borderWidth: 1,
			borderColor: hasError ? theme.colors.error : theme.colors.tertiary,
			borderRadius: theme.spacing.s1,
			paddingHorizontal: theme.spacing.s3,
		},
		input: {
			flex: 1,
			fontFamily: 'Montserrat',
			fontSize: 12,
			color: theme.colors.text,
			height: 50,
		},
		icon: {
			marginRight: iconPosition === 'left' ? theme.spacing.s2 : 0,
		},
		label: {
			color: theme.colors.text,
		},
		errorText: {
			color: theme.colors.error,
			fontSize: 12,
			marginTop: 4,
		},
	})

TextInput.displayName = 'TextInput'

export default TextInput
