import { Video } from 'expo-av'
import React, { forwardRef, useEffect, useRef, useState } from 'react'
import { ActivityIndicator, Animated, StyleSheet, TouchableOpacity, View } from 'react-native'
import { useTheme } from '../../../stores'
import { Theme, VideoPlayerProps } from '../../../types'
import Button from '../../atoms/Button'

interface ActionButtonProps {
	onPress: () => void
	icon: {
		library: string
		name: string
		size?: number
		color?: string
	}
	position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
	disabled?: boolean
}

const VideoPlayer = forwardRef<Video, VideoPlayerProps>(
	({ url, style, onChangeCamera, onShare, onDownload, onExpand, ...args }, ref) => {
		const videoRef = useRef<Video>(null)
		const finalRef = ref || videoRef
		const { theme } = useTheme()
		const styles = createStyles(theme)
		const [loading, setLoading] = useState(true)
		const [controlsVisible, setControlsVisible] = useState(true)
		const [isPlaying, setIsPlaying] = useState(true)
		const fadeAnim = React.useRef(new Animated.Value(1)).current
		const hideControlsTimer = useRef<NodeJS.Timeout | null>(null)

		const startHideTimer = () => {
			if (hideControlsTimer.current) {
				clearTimeout(hideControlsTimer.current)
			}
			hideControlsTimer.current = setTimeout(() => {
				Animated.timing(fadeAnim, {
					toValue: 0,
					duration: 200,
					useNativeDriver: true,
				}).start(() => setControlsVisible(false))
			}, 3000)
		}

		const toggleControls = () => {
			const toValue = controlsVisible ? 0 : 1
			setControlsVisible(!controlsVisible)

			if (!controlsVisible) {
				startHideTimer()
			} else if (hideControlsTimer.current) {
				clearTimeout(hideControlsTimer.current)
			}

			Animated.timing(fadeAnim, {
				toValue,
				duration: 200,
				useNativeDriver: true,
			}).start()
		}

		const togglePlayPause = async () => {
			if (videoRef.current) {
				if (isPlaying) {
					await videoRef.current.pauseAsync()
				} else {
					await videoRef.current.playAsync()
				}
				setIsPlaying(!isPlaying)
			}
			handleButtonPress()
		}

		useEffect(() => {
			if (!loading) {
				const timer = setTimeout(() => {
					Animated.timing(fadeAnim, {
						toValue: 0,
						duration: 200,
						useNativeDriver: true,
					}).start(() => setControlsVisible(false))
				}, 1000)

				return () => clearTimeout(timer)
			}
		}, [loading, fadeAnim])

		useEffect(
			() => () => {
				if (hideControlsTimer.current) {
					clearTimeout(hideControlsTimer.current)
				}
			},
			[]
		)

		const handleButtonPress = (callback?: () => void) => {
			if (hideControlsTimer.current) {
				clearTimeout(hideControlsTimer.current)
			}
			startHideTimer()
			if (callback) callback()
		}

		const renderActionButton = (
			position: ActionButtonProps['position'],
			onPress: () => void,
			icon: ActionButtonProps['icon']
		) => (
			<ActionButton
				position={position}
				onPress={() => controlsVisible && handleButtonPress(onPress)}
				icon={icon}
				disabled={!controlsVisible}
			/>
		)

		return (
			<View style={styles.container}>
				<Video
					ref={finalRef}
					source={{ uri: url }}
					style={StyleSheet.compose(styles.video, style)}
					useNativeControls={true}
					usePoster={true}
					shouldPlay
					isLooping
					resizeMode={'contain' as any}
					onLoadStart={() => setLoading(true)}
					onLoad={() => setLoading(false)}
					{...args}
				/>

				<TouchableOpacity activeOpacity={1} style={styles.touchableOverlay} onPress={toggleControls}>
					{loading && (
						<ActivityIndicator size={'large'} color={theme.colors.primary} style={styles.loading} />
					)}

					<Animated.View style={[styles.overlay, { opacity: fadeAnim }]}>
						{onChangeCamera &&
							renderActionButton('top-left', onChangeCamera, {
								library: 'Ionicons',
								name: 'camera-reverse-outline',
							})}

						{onShare &&
							renderActionButton('top-right', onShare, {
								library: 'AntDesign',
								name: 'sharealt',
							})}

						{/* Botão de play/pause no centro */}
						<Button
							type={'text'}
							onPress={togglePlayPause}
							icon={{
								library: 'AntDesign' as any,
								name: isPlaying ? 'pausecircleo' : 'playcircleo',
								size: 40,
								color: theme.colors.white,
							}}
							style={styles.playPauseButton}
						/>

						{onDownload &&
							renderActionButton('bottom-left', onDownload, {
								library: 'AntDesign',
								name: 'download',
							})}

						{onExpand &&
							renderActionButton('bottom-right', onExpand, {
								library: 'AntDesign',
								name: 'arrowsalt',
								size: 15,
								color: theme.colors.white,
							})}
					</Animated.View>
				</TouchableOpacity>
			</View>
		)
	}
)

const ActionButton = ({ onPress, icon, position = 'bottom-right', disabled }: ActionButtonProps) => {
	const { theme } = useTheme()
	const styles = createStyles(theme)

	const calculatePosition = () => {
		switch (position) {
			case 'top-left':
				return { top: theme.spacing.s1, left: theme.spacing.s1 }
			case 'top-right':
				return { top: theme.spacing.s1, right: theme.spacing.s1 }
			case 'bottom-left':
				return { bottom: theme.spacing.s1, left: theme.spacing.s1 }
			case 'bottom-right':
				return { bottom: theme.spacing.s1, right: theme.spacing.s1 }
			default:
				return { bottom: theme.spacing.s1, right: theme.spacing.s1 }
		}
	}

	return (
		<Button
			type={'text'}
			onPress={onPress}
			icon={{
				...icon,
				library: icon.library as any,
				size: 15,
				color: theme.colors.white,
			}}
			style={[styles.expandButton, calculatePosition(), disabled && styles.disabledButton]}
		/>
	)
}

VideoPlayer.displayName = 'VideoPlayer'

export default VideoPlayer

const createStyles = (theme: Theme) =>
	StyleSheet.create({
		container: {
			position: 'relative',
			justifyContent: 'center',
			alignItems: 'center',
		},
		touchableOverlay: {
			position: 'absolute',
			top: 0,
			left: 0,
			right: 0,
			bottom: 0,
			zIndex: 1,
			justifyContent: 'center',
			alignItems: 'center',
		},
		video: {
			width: '100%',
			aspectRatio: 16 / 9,
			borderRadius: theme.spacing.s1,
		},
		loading: {
			position: 'absolute',
			zIndex: 1,
		},
		expandButton: {
			position: 'absolute',
			width: 30,
			height: 30,
			borderRadius: 15,
			paddingVertical: 0,
			backgroundColor: theme.colors.secondary,
			alignItems: 'center',
			justifyContent: 'center',
		},
		overlay: {
			...StyleSheet.absoluteFillObject,
			backgroundColor: 'rgba(0,0,0,0.5)',
			justifyContent: 'center',
			alignItems: 'center',
		},
		disabledButton: {
			pointerEvents: 'none',
		},
		playPauseButton: {
			backgroundColor: 'transparent',
			alignItems: 'center',
			justifyContent: 'center',
		},
	})
