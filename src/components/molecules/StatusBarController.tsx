import { useEffect } from 'react'
import { StatusBar } from 'react-native'
import { useTheme } from '../../stores'

export default function StatusBarController() {
	const { theme } = useTheme()

	useEffect(() => {
		StatusBar.setBarStyle(theme.type === 'dark' ? 'light-content' : 'dark-content', true)
	}, [theme.type])

	return <StatusBar barStyle={theme.type === 'dark' ? 'light-content' : 'dark-content'} />
}
