import { LinearGradient } from 'expo-linear-gradient'
import React from 'react'
import { StyleSheet, TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native'
import { ActivityIndicator } from 'react-native-paper'

import { useTheme } from '../../../stores'
import type { Theme } from '../../../types'
import Icon from '../Icon'
import Text from '../Text'

type ButtonType = 'text' | 'outlined' | 'contained'
type IconLibrary =
	| 'AntDesign'
	| 'Entypo'
	| 'EvilIcons'
	| 'Feather'
	| 'FontAwesome'
	| 'FontAwesome5'
	| 'FontAwesome6'
	| 'Fontisto'
	| 'Ionicons'
	| 'MaterialCommunityIcons'
	| 'MaterialIcons'
	| 'Octicons'
	| 'SimpleLineIcons'

interface ButtonComponentProps {
	type?: ButtonType
	size?: 'small' | 'medium' | 'large'
	loading?: boolean
	label?: string
	icon?: {
		library?: IconLibrary
		name: string
		size?: 'extraSmall' | 'small' | 'medium' | 'large' | 'extraLarge' | number
		color?: string
	}
	onPress?: () => void
	style?: ViewStyle | ViewStyle[]
	textStyle?: TextStyle | TextStyle[]
	colors?: [string, string]
	children?: React.ReactNode
	accessibilityLabel?: string
}

const Button: React.FC<ButtonComponentProps> = ({
	type = 'contained',
	size = 'medium',
	loading = false,
	label,
	icon,
	onPress,
	style,
	textStyle,
	colors,
	children,
	...args
}) => {
	const { theme } = useTheme()
	const styles = createStyle(theme)

	const DefaultButton = () => (
		<TouchableOpacity
			style={StyleSheet.compose(styles.button[type], style)}
			onPress={onPress}
			accessible={true}
			accessibilityRole={'button'}
			accessibilityLabel={label || args.accessibilityLabel || 'Button'}
			{...args}
		>
			{loading ? (
				<ActivityIndicator
					style={{ marginHorizontal: theme.spacing.s1 }}
					size={15}
					color={theme.colors.white}
				/>
			) : (
				<View
					style={{
						flexDirection: 'row',
						alignItems: 'center',
						gap: theme.spacing.s1,
					}}
				>
					{icon && (
						<Icon
							library={icon.library}
							name={icon.name}
							size={icon.size}
							color={icon.color || theme.colors.secondary}
						/>
					)}

					{label && <Text style={StyleSheet.compose(styles.text[type], textStyle)}>{label}</Text>}

					{children}
				</View>
			)}
		</TouchableOpacity>
	)

	return type === 'text' || type === 'outlined' ? (
		<DefaultButton />
	) : (
		<LinearGradient
			colors={colors || [theme.colors.blue, theme.colors.primary]}
			start={[0, 1]}
			end={[1, 0]}
			style={{ borderRadius: theme.spacing.s1 }}
		>
			<DefaultButton />
		</LinearGradient>
	)
}

const createStyle = (theme: Theme) => {
	const buttonStyles = {
		contained: {
			flexDirection: 'row' as const,
			alignItems: 'center' as const,
			justifyContent: 'center' as const,
			borderRadius: theme.spacing.s1,
			minHeight: 40,
			paddingHorizontal: theme.spacing.s1,
		},
		text: {
			flexDirection: 'row' as const,
			alignItems: 'center' as const,
			justifyContent: 'center' as const,
			borderRadius: theme.spacing.s1,
			minHeight: 40,
			paddingHorizontal: theme.spacing.s1,
		},
		outlined: {
			borderWidth: 1,
			flexDirection: 'row' as const,
			alignItems: 'center' as const,
			justifyContent: 'center' as const,
			borderRadius: theme.spacing.s1,
			borderColor: theme.colors.primary,
			minHeight: 40,
			paddingHorizontal: theme.spacing.s1,
		},
	}

	const textStyles = {
		contained: {
			color: theme.colors.white,
			fontWeight: 'semibold' as const,
		},
		text: {
			color: theme.colors.white,
		},
		outlined: {
			color: theme.colors.white,
		},
	}

	return {
		button: StyleSheet.create(buttonStyles),
		text: StyleSheet.create(textStyles),
	}
}

export default Button
