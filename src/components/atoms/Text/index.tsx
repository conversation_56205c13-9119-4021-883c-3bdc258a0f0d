import React from 'react'
import { Text as RNText, StyleSheet, TextStyle } from 'react-native'
import { useTheme } from '../../../stores'

const fontVariants = {
	thin: 'MontserratThin',
	light: 'MontserratLight',
	regular: 'Montserrat',
	medium: 'MontserratMedium',
	semibold: 'MontserratSemiBold',
	bold: 'MontserratBold',
	extrabold: 'MontserratExtraBold',
}

interface TextProps {
	size?: string
	style?: any
	children?: React.ReactNode
	[key: string]: any
}

export default function Text({ size = 'medium', style, children, ...args }: TextProps) {
	const { theme } = useTheme()

	const styles = StyleSheet.create({
		font: {
			color: theme.colors.text,
			fontFamily: getFontVariant(style),
			letterSpacing: 0,
			fontSize: theme.fontSize[size] || theme.fontSize.medium,
			...StyleSheet.flatten(style),
		} as TextStyle,
	})

	return (
		<RNText style={styles.font} {...args}>
			{children}
		</RNText>
	)
}

function getFontVariant(style: any): string {
	const defaultWeights: Record<string, string> = {
		thin: 'thin',
		light: 'light',
		regular: 'regular',
		medium: 'medium',
		semibold: 'semibold',
		bold: 'bold',
		extrabold: 'extrabold',
	}

	const getFontWeightFromStyle = (styles: any): string | null => {
		if (Array.isArray(styles)) {
			for (let i = styles.length - 1; i >= 0; i--) {
				if (styles[i] && styles[i].fontWeight) {
					return styles[i].fontWeight
				}
			}
		} else if (styles && typeof styles === 'object' && styles.fontWeight) {
			return styles.fontWeight
		}
		return null
	}

	const fontWeight = getFontWeightFromStyle(style)
	if (fontWeight && defaultWeights[fontWeight]) {
		return fontVariants[defaultWeights[fontWeight]]
	}

	return fontVariants.regular
}
