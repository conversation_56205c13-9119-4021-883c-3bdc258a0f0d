import React, { useState } from 'react'
import { ActivityIndicator, TextStyle, View, ViewStyle } from 'react-native'
import NativeDropdown from 'react-native-element-dropdown/lib/module/components/Dropdown'
import { useTheme } from '../../../stores'
import type { IconProps, Theme } from '../../../types'
import Icon from '../Icon/Icon'
import Text from '../Text/Text'

interface DropdownItem {
	[key: string]: any
}

interface DropdownProps {
	data?: DropdownItem[]
	loading?: boolean
	label?: string
	labelIcon?: IconProps
	labelType?: string
	disabled?: boolean
	labelEmptyData?: string
	labelField?: string
	valueField?: string
	placeholder?: string
	style?: ViewStyle
	containerStyle?: ViewStyle
	icon?: IconProps
	value?: any
	error?: string
	onChange?: (item: DropdownItem) => void
	onValueChange?: (value: any) => void
	items?: DropdownItem[]
	children?: (
		item: DropdownItem,
		selected: boolean,
		styles: { CONTAINER: ViewStyle; TEXT: TextStyle }
	) => React.ReactNode
	[key: string]: any
}

export default function Dropdown({
	data,
	loading,
	label,
	labelIcon,
	labelType,
	disabled,
	labelEmptyData,
	labelField = 'label',
	valueField = 'value',
	placeholder,
	style,
	containerStyle,
	icon,
	value,
	error,
	onChange = () => {},
	onValueChange,
	items,
	children,
	...args
}: DropdownProps) {
	const { theme } = useTheme()

	const [isFocused, setIsFocused] = useState(false)

	const dropdownData = items || data || []
	const handleChange = (item: DropdownItem) => {
		if (onChange) onChange(item)
		if (onValueChange) onValueChange(item[valueField])
	}

	const styles = createStyle(theme, isFocused, disabled)

	const onFocus = () => setIsFocused(true)
	const onLoseFocus = () => setIsFocused(false)

	return (
		<View style={containerStyle}>
			{label && (
				<Text
					icon={labelIcon}
					type={labelType}
					color={(() => {
						if (disabled) return theme.colors.base200
						if (isFocused) return theme.colors.primary
						return theme.colors.textBody
					})()}
				>
					{label}
				</Text>
			)}

			<NativeDropdown
				disable={disabled}
				onChange={handleChange}
				data={
					dropdownData.length > 0
						? [
								{
									[labelField]:
										dropdownData.length > 0 ? placeholder : labelEmptyData || 'Nada para exibir',
									[valueField]: '',
								},
								...dropdownData,
							]
						: []
				}
				value={value}
				valueField={valueField}
				labelField={labelField}
				placeholder={placeholder}
				style={[styles.dropdown, style, error && styles.errorBorder]}
				itemTextStyle={styles.itemTextStyle}
				iconColor={theme.colors.text}
				selectedTextProps={{ numberOfLines: 1 }}
				placeholderStyle={styles.placeholderStyle}
				selectedTextStyle={styles.selectedTextStyle}
				inputSearchStyle={styles.inputSearchStyle}
				searchPlaceholder={'Digite para buscar'}
				onFocus={onFocus}
				onBlur={onLoseFocus}
				renderRightIcon={(visible: boolean) => {
					if (loading) return <ActivityIndicator size={'small'} color={theme.colors.text} />
					if (icon)
						return (
							<Icon name={icon.name} library={icon.library} size={icon.size as any} color={icon.color} />
						)

					return visible ? (
						<Icon name={'chevron-up'} library={'Entypo'} size={'small'} color={theme.colors.textBody} />
					) : (
						<Icon
							name={'chevron-down'}
							library={'Entypo'}
							size={'small'}
							color={isFocused ? theme.colors.textBody : theme.colors.base200}
						/>
					)
				}}
				renderItem={(item: DropdownItem, selected: boolean) =>
					children ? (
						children(item, selected, {
							CONTAINER: styles.renderItemView as ViewStyle,
							TEXT: styles.renderItemText as TextStyle,
						})
					) : (
						<View style={styles.renderItemView as ViewStyle}>
							<Text
								type={selected ? 'bodyHeavy' : undefined}
								numberOfLines={1}
								style={selected ? styles.renderTextSelected : styles.renderItemText}
							>
								{item[labelField]}
							</Text>
						</View>
					)
				}
				containerStyle={styles.containerStyle}
				{...args}
			/>

			{error && <Text style={styles.errorText}>{error}</Text>}
		</View>
	)
}

const createStyle = (theme: Theme, isFocused: boolean, disabled?: boolean) => ({
	dropdown: {
		height: 50,
		borderWidth: 1,
		borderColor: isFocused ? theme.colors.primary : theme.colors.base200,
		backgroundColor: !disabled ? theme.colors.secondary : theme.colors.base300,
		gap: theme.spacing.s4,
		paddingHorizontal: theme.spacing.s2,
		borderRadius: theme.spacing.s1,
	},
	errorBorder: {
		borderWidth: 1,
		borderColor: theme.colors.red,
	},
	itemTextStyle: {
		color: theme.colors.text,
	},
	placeholderStyle: {
		color: theme.colors.base200,
	},
	selectedTextStyle: {
		color: theme.colors.text,
	},
	inputSearchStyle: {
		borderRadius: theme.spacing.s2,
		color: theme.colors.text,
	},
	renderItemView: {
		flex: 1,
		flexDirection: 'row',
		alignItems: 'center',
	},
	renderItemText: {
		flex: 1,
		paddingVertical: theme.spacing.s1,
		borderRadius: theme.spacing.s2,
		color: theme.colors.textLight,
	},
	renderTextSelected: {
		flex: 1,
		backgroundColor: theme.colors.base400,
		paddingHorizontal: theme.spacing.s1,
		paddingVertical: theme.spacing.s1,
		color: theme.colors.textLight,
	},
	containerStyle: {
		maxHeight: 250,
		backgroundColor: theme.colors.secondary,
		borderWidth: 1,
		borderColor: isFocused ? theme.colors.primary : theme.colors.base200,
		borderTopWidth: 0,
		marginTop: -10,
		marginLeft: 0.5,
		paddingVertical: theme.spacing.s1,
		paddingHorizontal: theme.spacing.s2,
		borderBottomLeftRadius: theme.spacing.s3,
		borderBottomRightRadius: theme.spacing.s3,
	},
	errorText: {
		fontSize: theme.fontSize.small,
		color: theme.colors.red,
	},
})
