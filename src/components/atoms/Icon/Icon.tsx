import {
	<PERSON>t<PERSON>esign,
	<PERSON>ty<PERSON>,
	EvilIcons,
	Feather,
	FontAwesome,
	FontAwesome5,
	FontAwesome6,
	Fontisto,
	Ionicons,
	MaterialCommunityIcons,
	MaterialIcons,
	Octicons,
	SimpleLineIcons,
} from '@expo/vector-icons'
import { TouchableOpacity } from 'react-native'

import { useTheme } from '../../../stores'
import type { IconComponentProps } from '../../../types'

type IconLibrary = keyof typeof iconLibraries
type IconSize = 'extraSmall' | 'small' | 'medium' | 'large' | 'extraLarge'

const iconLibraries = {
	AntDesign,
	Entypo,
	EvilIcons,
	Ionicons,
	FontAwesome,
	FontAwesome5,
	FontAwesome6,
	Fontisto,
	MaterialCommunityIcons,
	MaterialIcons,
	Feather,
	SimpleLineIcons,
	Octicons,
	default: FontAwesome,
}

interface IconProps extends Omit<IconComponentProps, 'library' | 'size'> {
	name: string
	library?: IconLibrary
	size?: IconSize | number
	color?: string
	style?: any
	onPress?: () => void
}

const Icon: React.FC<IconProps> = ({ name, library, size = 'medium', color, style, onPress, ...args }) => {
	const { theme } = useTheme()
	const IconComponent = iconLibraries[library || 'default'] || iconLibraries.default

	function getIconSize(): number {
		if (typeof size === 'number') {
			return size
		}

		switch (size) {
			case 'extraSmall':
				return theme.fontSize.extraSmall * 1.5
			case 'small':
				return theme.fontSize.small * 1.5
			case 'medium':
				return theme.fontSize.medium * 1.5
			case 'large':
				return theme.fontSize.large * 1.5
			case 'extraLarge':
				return theme.fontSize.extraLarge * 1.5
			default:
				return theme.fontSize.medium * 1.5
		}
	}

	const iconElement = (
		<IconComponent
			{...args}
			name={name?.toLowerCase()}
			size={getIconSize()}
			color={color || theme.colors.text}
			style={style}
		/>
	)

	if (onPress) {
		return <TouchableOpacity onPress={onPress}>{iconElement}</TouchableOpacity>
	}

	return iconElement
}

export default Icon
