import {
	Keyboard,
	KeyboardAvoidingView,
	Platform,
	SafeAreaView,
	ScrollView,
	TouchableWithoutFeedback,
	View,
} from 'react-native'

import { createSignUpStyles } from './core/signUp.styles'

import SignUpForm from './components/SignUpForm'
import SignUpHeader from './components/SignUpHeader'

export default function SignUp() {
	const styles = createSignUpStyles()

	return (
		<SafeAreaView style={styles.safeArea}>
			<KeyboardAvoidingView
				behavior={Platform.OS === 'ios' ? 'padding' : undefined}
				style={styles.keyboardAvoidView}
				keyboardVerticalOffset={0}
				enabled={Platform.OS === 'ios'}
			>
				<TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
					<ScrollView
						contentContainerStyle={styles.scrollContainer}
						bounces={false}
						keyboardShouldPersistTaps="handled"
						showsVerticalScrollIndicator={false}
					>
						<View style={styles.container}>
							<SignUpHeader />
							<SignUpForm />

							{/* Add padding at the bottom to ensure scrolling works well with keyboard */}
							<View style={styles.bottomPadding} />
						</View>
					</ScrollView>
				</TouchableWithoutFeedback>
			</KeyboardAvoidingView>
		</SafeAreaView>
	)
}
