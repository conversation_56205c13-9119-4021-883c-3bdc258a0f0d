/**
 * Configurações dos campos do formulário
 * Centraliza todas as configurações dos inputs
 */
export const SIGNUP_FORM_CONFIG = {
	name: {
		label: 'Nome',
		placeholder: 'Digite seu nome',
		field: 'name',
		autoCompleteType: 'name',
		textContentType: 'name',
	},
	email: {
		label: 'E-mail',
		placeholder: 'Digite seu e-mail',
		field: 'email',
		autoCompleteType: 'email',
		textContentType: 'emailAddress',
		keyboardType: 'email-address',
	},
	password: {
		label: 'Senha',
		placeholder: '**********',
		field: 'password',
		autoCompleteType: 'password',
		textContentType: 'password',
		secureTextEntry: true,
	},
	passwordConfirmation: {
		label: 'Confirme sua senha',
		placeholder: '**********',
		field: 'passwordConfirmation',
		autoCompleteType: 'password',
		textContentType: 'password',
		secureTextEntry: true,
	},
	whatsapp: {
		label: 'WhatsApp',
		placeholder: '(XX) XXXXX-XXXX',
		field: 'whatsapp',
		keyboardType: 'phone-pad',
		autoCompleteType: 'tel',
		textContentType: 'telephoneNumber',
		mask: 'whatsapp',
	},
	terms: {
		label: 'Declaro que li e estou de acordo com os termos de uso',
		field: 'terms',
	},
} as const

/**
 * Mensagens de validação do formulário
 */
export const SIGNUP_VALIDATION_MESSAGES = {
	REQUIRED_FIELD: 'Campo obrigatório',
	PASSWORDS_DONT_MATCH: 'As senhas não são iguais',
	TERMS_REQUIRED: 'Aceite os termos de uso para continuar',
	SIGNUP_ERROR: 'Erro ao cadastrar',
	SIGNUP_ERROR_MESSAGE: 'Tente novamente mais tarde',
} as const

/**
 * Configurações de navegação
 */
export const SIGNUP_ROUTES = {
	LOGIN: 'Login',
} as const

/**
 * Textos estáticos da tela
 */
export const SIGNUP_TEXTS = {
	HEADER_TITLE: 'CADASTRE-SE',
	SIGNUP_BUTTON: 'CADASTRAR',
	SUCCESS_MESSAGE: 'Cadastro realizado com sucesso!',
} as const

/**
 * Configurações de API
 */
export const SIGNUP_API_CONFIG = {
	ENDPOINT: '/users/player/insert',
	WHATSAPP_PREFIX: '55',
	SECTION: 'signUp',
} as const
