import { Platform, StyleSheet } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

import { useTheme } from '../../../../stores'

export const createSignUpStyles = () => {
	const { theme } = useTheme()
	const insets = useSafeAreaInsets()

	return StyleSheet.create({
		safeArea: {
			flex: 1,
			backgroundColor: theme.colors.background,
			paddingTop: insets.top,
			paddingBottom: insets.bottom,
		},
		keyboardAvoidView: {
			flex: 1,
		},
		scrollContainer: {
			flexGrow: 1,
		},
		container: {
			flexGrow: 1,
			justifyContent: 'center',
			gap: theme.spacing.s6,
			paddingVertical: theme.spacing.s1,
			paddingHorizontal: theme.spacing.s4,
			paddingBottom: Platform.OS === 'android' ? 40 : theme.spacing.s1,
		},
		header: {
			alignItems: 'flex-start',
		},
		btnBack: {
			width: 50,
			height: 50,
			borderRadius: 25,
			paddingVertical: 0,
			justifyContent: 'flex-start',
		},
		textHeader: {
			color: theme.colors.primary,
			fontSize: theme.fontSize.extraLarge,
			fontWeight: 'semibold',
			textAlign: 'center',
			alignSelf: 'center',
		},
		form: {
			justifyContent: 'center',
			gap: theme.spacing.s2,
		},
		bottomPadding: {
			height: 20,
		},
	})
}
