import { create } from 'zustand'

interface SignUpErrors {
	name: string | null
	email: string | null
	password: string | null
	passwordConfirmation: string | null
	whatsapp: string | null
}

interface SignUpState {
	name: string
	email: string
	password: string
	passwordConfirmation: string
	whatsapp: string
	whatsappFormatted: string
	terms: boolean
	showPasswords: boolean
	errors: SignUpErrors
}

interface SignUpActions {
	updateField: (field: keyof Omit<SignUpState, 'errors' | 'whatsappFormatted'>, value: string | boolean) => void
	updateWhatsapp: (rawValue: string, formattedValue: string) => void
	toggleField: (field: keyof Pick<SignUpState, 'terms' | 'showPasswords'>) => void
	setErrors: (errors: Partial<SignUpErrors>) => void
	clearErrors: () => void
	resetForm: () => void
}

type SignUpStore = SignUpState & SignUpActions

const initialState: SignUpState = {
	name: '',
	email: '',
	password: '',
	passwordConfirmation: '',
	whatsapp: '',
	whatsappFormatted: '',
	terms: false,
	showPasswords: false,
	errors: {
		name: null,
		email: null,
		password: null,
		passwordConfirmation: null,
		whatsapp: null,
	},
}

export const useSignUpStore = create<SignUpStore>((set) => ({
	...initialState,

	updateField: (field, value) =>
		set((state) => ({
			[field]: value,
			errors: {
				...state.errors,
				[field]: null,
			},
		})),

	updateWhatsapp: (rawValue, formattedValue) =>
		set((state) => ({
			whatsapp: rawValue,
			whatsappFormatted: formattedValue,
			errors: {
				...state.errors,
				whatsapp: null,
			},
		})),

	toggleField: (field) => set((state) => ({ [field]: !state[field] })),

	setErrors: (errors) =>
		set((state) => ({
			errors: { ...state.errors, ...errors },
		})),

	clearErrors: () =>
		set({
			errors: {
				name: null,
				email: null,
				password: null,
				passwordConfirmation: null,
				whatsapp: null,
			},
		}),

	resetForm: () => set(initialState),
}))
