import { SIGNUP_VALIDATION_MESSAGES } from './signUp.constants'

interface FormHandlers {
	handleTextChange: (field: string) => (value: string) => void
	handleToggle: (field: string) => () => void
	handleWhatsappChange: (value: string, rawValue?: string) => void
}

interface ValidationResult {
	isValid: boolean
	errors: Record<string, string>
	termsError?: string | null
}

interface FormData {
	name: string
	email: string
	password: string
	passwordConfirmation: string
	whatsapp: string
	terms: boolean
}

/**
 * Cria handlers genéricos para formulários
 * @param updateField - Função para atualizar campos
 * @param toggleField - Função para alternar campos boolean
 * @param updateWhatsapp - Função para atualizar WhatsApp
 * @returns Objeto com handlers genéricos
 */
export const createFormHandlers = (
	updateField: (field: string, value: string) => void,
	toggleField: (field: string) => void,
	updateWhatsapp: (rawValue: string, formattedValue: string) => void
): FormHandlers => ({
	handleTextChange: (field: string) => (value: string) => updateField(field, value),
	handleToggle: (field: string) => () => toggleField(field),
	handleWhatsappChange: (value: string, rawValue?: string) => {
		const cleanedRawValue = rawValue || value.replace(/\D/g, '')
		updateWhatsapp(cleanedRawValue, value)
	},
})

/**
 * Valida os dados do formulário de cadastro
 * @param formData - Dados do formulário
 * @returns Resultado da validação com isValid e errors
 */
export const validateSignUpForm = (formData: FormData): ValidationResult => {
	// Verificar se formData é válido
	if (!formData || typeof formData !== 'object') {
		return {
			isValid: false,
			errors: { general: 'Dados do formulário inválidos' },
		}
	}

	const { name, email, password, passwordConfirmation, whatsapp, terms } = formData
	const errors: Record<string, string> = {}

	// Validação de campos obrigatórios
	if (!name || typeof name !== 'string' || !name.trim()) {
		errors.name = SIGNUP_VALIDATION_MESSAGES.REQUIRED_FIELD
	}
	if (!email || typeof email !== 'string' || !email.trim()) {
		errors.email = SIGNUP_VALIDATION_MESSAGES.REQUIRED_FIELD
	}
	if (!password || typeof password !== 'string' || !password.trim()) {
		errors.password = SIGNUP_VALIDATION_MESSAGES.REQUIRED_FIELD
	}
	if (!passwordConfirmation || typeof passwordConfirmation !== 'string' || !passwordConfirmation.trim()) {
		errors.passwordConfirmation = SIGNUP_VALIDATION_MESSAGES.REQUIRED_FIELD
	}
	if (!whatsapp || typeof whatsapp !== 'string' || !whatsapp.trim()) {
		errors.whatsapp = SIGNUP_VALIDATION_MESSAGES.REQUIRED_FIELD
	}

	// Validação de senhas iguais
	if (password && passwordConfirmation && password !== passwordConfirmation) {
		errors.password = SIGNUP_VALIDATION_MESSAGES.PASSWORDS_DONT_MATCH
		errors.passwordConfirmation = SIGNUP_VALIDATION_MESSAGES.PASSWORDS_DONT_MATCH
	}

	// Validação de termos de uso
	if (!terms) {
		return {
			isValid: false,
			errors,
			termsError: SIGNUP_VALIDATION_MESSAGES.TERMS_REQUIRED,
		}
	}

	return {
		isValid: Object.keys(errors).length === 0,
		errors,
		termsError: null,
	}
}

/**
 * Mapeia erros da API para os campos do formulário
 * @param apiErrors - Erros retornados pela API
 * @returns Erros mapeados para os campos do formulário
 */
export const mapApiErrorsToForm = (apiErrors: Record<string, string[]>): Record<string, string> => {
	const formErrors: Record<string, string> = {}

	Object.entries(apiErrors).forEach(([field, messages]) => {
		if (Array.isArray(messages) && messages.length > 0) {
			if (field === 'password_confirmation') {
				;[formErrors.passwordConfirmation] = messages
			} else {
				;[formErrors[field]] = messages
			}
		}
	})

	return formErrors
}

/**
 * Cria ícone para campos de senha com visibilidade
 * @param showPasswords - Estado de visibilidade das senhas
 * @param theme - Tema da aplicação
 * @param toggleShowPasswords - Função para alternar visibilidade
 * @returns Configuração do ícone
 */
export const createPasswordIcon = (showPasswords: boolean, theme: any, toggleShowPasswords: () => void) => ({
	name: showPasswords ? 'eye-off' : 'eye',
	library: 'Feather',
	color: theme.colors.text,
	size: 'small',
	onPress: toggleShowPasswords,
})
