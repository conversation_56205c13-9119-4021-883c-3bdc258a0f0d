import { useNavigation } from '@react-navigation/native'
import { useCallback } from 'react'

import { ErrorMessage, SuccessMessage } from '../../../../helpers/HandleMessages'
import { useApi, useTheme } from '../../../../stores'

import { SIGNUP_API_CONFIG, SIGNUP_ROUTES, SIGNUP_TEXTS } from './signUp.constants'
import { signUpUser } from './signUp.service'
import { useSignUpStore } from './signUp.store'

import { createFormHandlers, createPasswordIcon, validateSignUpForm } from './signUp.utils'

export const useSignUp = () => {
	const { theme } = useTheme()
	const { sectionLoading } = useApi()
	const navigation = useNavigation<any>()

	const {
		name,
		email,
		password,
		passwordConfirmation,
		whatsapp,
		whatsappFormatted,
		terms,
		showPasswords,
		errors,
		updateField,
		updateWhatsapp,
		toggleField,
		setErrors,
		clearErrors,
		resetForm,
	} = useSignUpStore()

	const handleSignUp = useCallback(async (): Promise<void> => {
		clearErrors()

		const formData = {
			name,
			email,
			password,
			passwordConfirmation,
			whatsapp,
			terms,
		}

		const validation = validateSignUpForm(formData)

		if (!validation.isValid) {
			if (validation.termsError) {
				ErrorMessage({
					title: 'Erro de validação',
					message: validation.termsError,
				})
			}
			if (Object.keys(validation.errors).length > 0) {
				setErrors(validation.errors)
			}
			return
		}

		const data = await signUpUser({
			name,
			email,
			password,
			passwordConfirmation,
			whatsapp,
		})

		if (data) {
			SuccessMessage({ message: data?.message || SIGNUP_TEXTS.SUCCESS_MESSAGE })
			resetForm()
			navigation.navigate(SIGNUP_ROUTES.LOGIN)
		}
	}, [name, email, password, passwordConfirmation, whatsapp, terms, clearErrors, setErrors, resetForm, navigation])

	const navigateToLogin = useCallback((): void => {
		navigation.navigate(SIGNUP_ROUTES.LOGIN)
	}, [navigation])

	const toggleShowPasswords = useCallback((): void => {
		toggleField('showPasswords')
	}, [toggleField])

	// Ícone para campos de senha
	const passwordIcon = () => createPasswordIcon(showPasswords, theme, toggleShowPasswords)

	return {
		// States
		name,
		email,
		password,
		passwordConfirmation,
		whatsapp,
		whatsappFormatted,
		terms,
		showPasswords,
		errors,
		loading: sectionLoading[SIGNUP_API_CONFIG.SECTION],

		// Generic handlers
		updateField,
		updateWhatsapp,
		toggleField,

		// Form handlers
		...createFormHandlers(updateField, toggleField, updateWhatsapp),

		// Specific actions
		handleSignUp,
		toggleShowPasswords,

		// Navigation
		navigateToLogin,

		// UI helpers
		passwordIcon,
	}
}
