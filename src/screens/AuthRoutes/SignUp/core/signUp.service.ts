import { useApi } from '../../../../stores'
import { SIGNUP_API_CONFIG } from './signUp.constants'

const { callApi } = useApi.getState()

interface SignUpUserData {
	name: string
	email: string
	password: string
	passwordConfirmation: string
	whatsapp: string
}

/**
 * Realiza o cadastro do usuário
 * @param userData - Dados do usuário
 * @returns Resultado da chamada de API
 */
export const signUpUser = async ({
	name,
	email,
	password,
	passwordConfirmation,
	whatsapp,
}: SignUpUserData): Promise<any> => {
	// Formata o número para o padrão da API (apenas números com 55 na frente)
	const formattedWhatsapp = whatsapp.startsWith(SIGNUP_API_CONFIG.WHATSAPP_PREFIX)
		? whatsapp
		: `${SIGNUP_API_CONFIG.WHATSAPP_PREFIX}${whatsapp}`

	return callApi({
		method: 'POST',
		section: SIGNUP_API_CONFIG.SECTION,
		url: SIGNUP_API_CONFIG.ENDPOINT,
		body: {
			name,
			email,
			password,
			password_confirmation: passwordConfirmation,
			whatsapp: formattedWhatsapp,
		},
	})
}
