import { View } from 'react-native'

import { But<PERSON>, Check, TextInput } from '../../../../components'

import { SIGNUP_FORM_CONFIG, SIGNUP_TEXTS } from '../core/signUp.constants'
import { useSignUp } from '../core/signUp.hook'
import { createSignUpStyles } from '../core/signUp.styles'

const SignUpForm: React.FC = () => {
	const {
		name,
		email,
		password,
		passwordConfirmation,
		whatsappFormatted,
		terms,
		showPasswords,
		loading,
		errors,
		passwordIcon,
		handleTextChange,
		handleWhatsappChange,
		handleToggle,
		handleSignUp,
	} = useSignUp()

	const styles = createSignUpStyles()

	return (
		<View style={styles.form}>
			<TextInput
				value={name}
				label={SIGNUP_FORM_CONFIG.name.label}
				placeholder={SIGNUP_FORM_CONFIG.name.placeholder}
				onChangeText={handleTextChange('name')}
				error={errors.name}
				autoCompleteType={SIGNUP_FORM_CONFIG.name.autoCompleteType}
				textContentType={SIGNUP_FORM_CONFIG.name.textContentType}
			/>

			<TextInput
				value={email}
				label={SIGNUP_FORM_CONFIG.email.label}
				placeholder={SIGNUP_FORM_CONFIG.email.placeholder}
				onChangeText={handleTextChange('email')}
				error={errors.email}
				autoCompleteType={SIGNUP_FORM_CONFIG.email.autoCompleteType}
				textContentType={SIGNUP_FORM_CONFIG.email.textContentType}
				keyboardType={SIGNUP_FORM_CONFIG.email.keyboardType}
			/>

			<TextInput
				value={password}
				label={SIGNUP_FORM_CONFIG.password.label}
				placeholder={SIGNUP_FORM_CONFIG.password.placeholder}
				secureTextEntry={!showPasswords}
				onChangeText={handleTextChange('password')}
				iconPosition={'right'}
				error={errors.password}
				autoCompleteType={SIGNUP_FORM_CONFIG.password.autoCompleteType}
				textContentType={SIGNUP_FORM_CONFIG.password.textContentType}
				icon={passwordIcon()}
			/>

			<TextInput
				value={passwordConfirmation}
				label={SIGNUP_FORM_CONFIG.passwordConfirmation.label}
				placeholder={SIGNUP_FORM_CONFIG.passwordConfirmation.placeholder}
				secureTextEntry={!showPasswords}
				onChangeText={handleTextChange('passwordConfirmation')}
				iconPosition={'right'}
				error={errors.passwordConfirmation}
				autoCompleteType={SIGNUP_FORM_CONFIG.passwordConfirmation.autoCompleteType}
				textContentType={SIGNUP_FORM_CONFIG.passwordConfirmation.textContentType}
				icon={passwordIcon()}
			/>

			<TextInput
				value={whatsappFormatted}
				label={SIGNUP_FORM_CONFIG.whatsapp.label}
				placeholder={SIGNUP_FORM_CONFIG.whatsapp.placeholder}
				onChangeText={handleWhatsappChange}
				mask={SIGNUP_FORM_CONFIG.whatsapp.mask}
				error={errors.whatsapp}
				keyboardType={SIGNUP_FORM_CONFIG.whatsapp.keyboardType}
				autoCompleteType={SIGNUP_FORM_CONFIG.whatsapp.autoCompleteType}
				textContentType={SIGNUP_FORM_CONFIG.whatsapp.textContentType}
			/>

			<Check label={SIGNUP_FORM_CONFIG.terms.label} checked={terms} onPress={handleToggle('terms')} />

			<Button loading={loading} onPress={handleSignUp} label={SIGNUP_TEXTS.SIGNUP_BUTTON} />
		</View>
	)
}

export default SignUpForm
