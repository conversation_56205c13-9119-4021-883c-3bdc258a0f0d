import React from 'react'
import { View } from 'react-native'

import { Button, Text } from '../../../../components'
import { useTheme } from '../../../../stores'

import { SIGNUP_TEXTS } from '../core/signUp.constants'
import { useSignUp } from '../core/signUp.hook'
import { createSignUpStyles } from '../core/signUp.styles'

const SignUpHeader: React.FC = () => {
	const { theme } = useTheme()
	const { navigateToLogin } = useSignUp()
	const styles = createSignUpStyles()

	return (
		<View style={styles.header}>
			<Button
				type={'text'}
				style={styles.btnBack}
				onPress={navigateToLogin}
				icon={{
					name: 'arrow-left',
					color: theme.colors.white,
				}}
			/>
			<Text style={styles.textHeader}>{SIGNUP_TEXTS.HEADER_TITLE}</Text>
		</View>
	)
}

export default React.memo(SignUpHeader)
