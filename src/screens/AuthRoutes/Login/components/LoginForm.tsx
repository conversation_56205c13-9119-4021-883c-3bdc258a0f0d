import React from 'react'
import { View } from 'react-native'

import { But<PERSON>, Check, TextInput } from '../../../../components'
import { useApi, useTheme } from '../../../../stores'
import { AutoCapitalizeType, KeyboardType } from '../../../../types/auth'

import { LOGIN_API_CONFIG, LOGIN_FORM_CONFIG, LOGIN_TEXTS } from '../core/login.constants'
import { createLoginStyles } from '../core/login.styles'
import { useLogin } from '../core/login.hook'

// Tipos temporários para os componentes até a migração completa
interface LoginTextInputProps {
	value: string
	label: string
	placeholder: string
	keyboardType?: KeyboardType
	autoCapitalize?: AutoCapitalizeType
	onChangeText: (text: string) => void
	secureTextEntry?: boolean
	iconPosition?: string
	icon?: {
		library: string
		name: string
		color: string
		onPress: () => void
	}
}

interface LoginCheckProps {
	checked: boolean
	onPress: () => void
	label: string
}

interface LoginButtonProps {
	type?: string
	label: string
	loading?: boolean
	onPress: () => void | Promise<void>
	textStyle?: any
}

// Componentes tipados temporariamente
const TypedTextInput = TextInput as React.ComponentType<LoginTextInputProps>
const TypedCheck = Check as React.ComponentType<LoginCheckProps>
const TypedButton = Button as React.ComponentType<LoginButtonProps>

const LoginForm: React.FC = () => {
	const { sectionLoading } = useApi()
	const { theme } = useTheme()
	const {
		email,
		password,
		remember,
		passwordVisible,
		handleLogin,
		handleTextChange,
		handleToggle,
		navigateToForgotPassword,
	} = useLogin()

	const styles = createLoginStyles()

	return (
		<View style={styles.body}>
			<TypedTextInput
				value={email}
				label={LOGIN_FORM_CONFIG.email.label}
				placeholder={LOGIN_FORM_CONFIG.email.placeholder}
				keyboardType={LOGIN_FORM_CONFIG.email.keyboardType}
				autoCapitalize={LOGIN_FORM_CONFIG.email.autoCapitalize}
				onChangeText={handleTextChange('email')}
			/>

			<TypedTextInput
				value={password}
				secureTextEntry={!passwordVisible}
				label={LOGIN_FORM_CONFIG.password.label}
				placeholder={LOGIN_FORM_CONFIG.password.placeholder}
				autoCapitalize={LOGIN_FORM_CONFIG.password.autoCapitalize}
				onChangeText={handleTextChange('password')}
				iconPosition={'right'}
				icon={{
					library: 'Feather',
					name: passwordVisible ? 'eye' : 'eye-off',
					color: theme.colors.textLight,
					onPress: handleToggle('passwordVisible'),
				}}
			/>

			<View style={styles.containerRemember}>
				<TypedCheck checked={remember} onPress={handleToggle('remember')} label={LOGIN_FORM_CONFIG.remember.label} />

				<TypedButton
					type={'text'}
					label={LOGIN_TEXTS.FORGOT_PASSWORD}
					textStyle={styles.forgotPassword}
					onPress={navigateToForgotPassword}
				/>
			</View>

			<TypedButton
				label={LOGIN_TEXTS.LOGIN_BUTTON}
				loading={sectionLoading[LOGIN_API_CONFIG.SECTION]}
				onPress={handleLogin}
			/>
		</View>
	)
}

export default LoginForm
