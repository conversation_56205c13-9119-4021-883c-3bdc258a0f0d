import React from 'react'
import { TouchableOpacity, View } from 'react-native'

import { Text } from '../../../../components'

import { createLoginStyles } from '../core/login.styles'
import { LOGIN_TEXTS } from '../core/login.constants'
import { useLogin } from '../core/login.hook'

const LoginFooter: React.FC = () => {
	const { navigateToSignUp } = useLogin()

	const styles = createLoginStyles()

	return (
		<View style={styles.footer}>
			<TouchableOpacity onPress={navigateToSignUp} style={styles.footerButton}>
				<Text size={'small'} style={styles.textFooter}>
					{LOGIN_TEXTS.FOOTER_TEXT}

					<Text style={styles.textSignUp}>{LOGIN_TEXTS.FOOTER_SIGNUP}</Text>
				</Text>
			</TouchableOpacity>
		</View>
	)
}

export default LoginFooter
