import React from 'react'
import { View } from 'react-native'

import { But<PERSON>, Check, TextInput } from '../../../../components'
import { useApi, useTheme } from '../../../../stores'

import { LOGIN_API_CONFIG, LOGIN_FORM_CONFIG, LOGIN_TEXTS } from '../core/login.constants'
import { createLoginStyles } from '../core/login.styles'
import { useLogin } from '../core/login.hook'

const LoginForm: React.FC = () => {
	const { sectionLoading } = useApi()
	const { theme } = useTheme()
	const {
		email,
		password,
		remember,
		passwordVisible,
		handleLogin,
		handleTextChange,
		handleToggle,
		navigateToForgotPassword,
	} = useLogin()

	const styles = createLoginStyles()

	return (
		<View style={styles.body}>
			<TextInput
				value={email}
				label={LOGIN_FORM_CONFIG.email.label}
				placeholder={LOGIN_FORM_CONFIG.email.placeholder}
				keyboardType={LOGIN_FORM_CONFIG.email.keyboardType as any}
				autoCapitalize={LOGIN_FORM_CONFIG.email.autoCapitalize as any}
				onChangeText={handleTextChange('email')}
				{...({} as any)}
			/>

			<TextInput
				value={password}
				secureTextEntry={!passwordVisible}
				label={LOGIN_FORM_CONFIG.password.label}
				placeholder={LOGIN_FORM_CONFIG.password.placeholder}
				autoCapitalize={LOGIN_FORM_CONFIG.password.autoCapitalize as any}
				onChangeText={handleTextChange('password')}
				iconPosition={'right'}
				icon={{
					library: 'Feather',
					name: passwordVisible ? 'eye' : 'eye-off',
					color: theme.colors.textLight,
					onPress: handleToggle('passwordVisible'),
				}}
				{...({} as any)}
			/>

			<View style={styles.containerRemember}>
				<Check
					checked={remember}
					onPress={handleToggle('remember')}
					label={LOGIN_FORM_CONFIG.remember.label}
					{...({} as any)}
				/>

				<Button
					type={'text'}
					label={LOGIN_TEXTS.FORGOT_PASSWORD}
					textStyle={styles.forgotPassword}
					onPress={navigateToForgotPassword}
					{...({} as any)}
				/>
			</View>

			<Button
				label={LOGIN_TEXTS.LOGIN_BUTTON}
				loading={sectionLoading[LOGIN_API_CONFIG.SECTION]}
				onPress={handleLogin}
				{...({} as any)}
			/>
		</View>
	)
}

export default LoginForm
