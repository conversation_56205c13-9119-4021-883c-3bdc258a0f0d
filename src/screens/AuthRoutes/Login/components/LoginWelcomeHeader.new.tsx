import React from 'react'
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated'

import { Text } from '../../../../components'
import { LOGIN_TEXTS } from '../core/login.constants'
import { createLoginStyles } from '../core/login.styles'

const LoginWelcomeHeader: React.FC = () => {
	const styles = createLoginStyles()

	return (
		<Animated.View style={styles.header} entering={FadeInDown} exiting={FadeInUp}>
			<Text size={'extraLarge'} style={styles.welcomeText}>
				{LOGIN_TEXTS.WELCOME_TITLE} <Text style={styles.welcomeTextFocus}>{LOGIN_TEXTS.WELCOME_FOCUS}</Text>
			</Text>

			<Text size={'small'}>{LOGIN_TEXTS.WELCOME_SUBTITLE}</Text>
		</Animated.View>
	)
}

export default LoginWelcomeHeader
