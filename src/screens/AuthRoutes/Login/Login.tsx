import React from 'react'
import { KeyboardAvoidingView, Platform, ScrollView, View } from 'react-native'
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context'

import { createLoginStyles } from './core/login.styles'

import LoginWelcomeHeader from './components/LoginWelcomeHeader'
import LoginForm from './components/LoginForm'
import LoginFooter from './components/LoginFooter'

const Login: React.FC = () => {
	const styles = createLoginStyles()
	const insets = useSafeAreaInsets()

	return (
		<KeyboardAvoidingView
			style={styles.container}
			behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
			keyboardVerticalOffset={insets.top}
		>
			<SafeAreaView style={{ flex: 1 }}>
				<ScrollView
					style={styles.scrollView}
					contentContainerStyle={styles.scrollContent}
					showsVerticalScrollIndicator={false}
					keyboardShouldPersistTaps={'handled'}
				>
					<View style={styles.contain}>
						<LoginWelcomeHeader />
						<LoginForm />
					</View>
				</ScrollView>
				<LoginFooter />
			</SafeAreaView>
		</KeyboardAvoidingView>
	)
}

export default Login
