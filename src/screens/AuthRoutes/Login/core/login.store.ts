import { create } from 'zustand'
import { LoginState, User } from '../../../../types/auth'

interface UserData extends Partial<User> {
	password?: string
}

interface LoginStore extends LoginState {
	updateField: (field: keyof LoginState, value: string | boolean) => void
	toggleField: (field: keyof LoginState) => void
	resetForm: () => void
	setUserData: (userData: UserData) => void
}

const initialState: LoginState = {
	email: '',
	password: '',
	remember: false,
	passwordVisible: false,
}

export const useLoginStore = create<LoginStore>((set) => ({
	...initialState,

	updateField: (field, value) => set({ [field]: value }),
	toggleField: (field) => set((state) => ({ [field]: !state[field] })),
	resetForm: () => set(initialState),

	// Atualizar dados do usuário salvos
	setUserData: (userData) =>
		set({
			email: userData.email || '',
			password: userData.password || '',
			remember: true,
		}),
}))
