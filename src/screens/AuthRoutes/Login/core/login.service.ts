import { useApi } from '../../../../stores'
import { AuthResponse } from '../../../../types/auth'
import { LOGIN_API_CONFIG } from './login.constants'

const { callApi } = useApi.getState()

/**
 * Realiza o login do usuário
 * @param email - Email do usuário
 * @param password - Senha do usuário
 * @returns Resultado da chamada de API
 */
export const login = async (email: string, password: string): Promise<AuthResponse | null> =>
	callApi({
		method: 'POST',
		section: LOGIN_API_CONFIG.SECTION,
		url: '/users/player/login',
		body: {
			email,
			password,
		},
	})
