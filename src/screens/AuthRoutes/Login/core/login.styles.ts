import { StyleSheet, TextStyle, ViewStyle } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

import { useTheme } from '../../../../stores'

interface LoginStyles {
	container: ViewStyle
	scrollView: ViewStyle
	scrollContent: ViewStyle
	contain: ViewStyle
	header: ViewStyle
	welcomeText: TextStyle
	welcomeTextFocus: TextStyle
	body: ViewStyle
	containerRemember: ViewStyle
	forgotPassword: TextStyle
	footer: ViewStyle
	footerButton: ViewStyle
	textFooter: TextStyle
	textSignUp: TextStyle
}

export const createLoginStyles = (): LoginStyles => {
	const { theme } = useTheme()
	const insets = useSafeAreaInsets()

	return StyleSheet.create({
		container: {
			flex: 1,
			paddingBottom: insets.bottom,
		},
		scrollView: {
			flex: 1,
		},
		scrollContent: {
			flexGrow: 1,
			justifyContent: 'center',
		},
		contain: {
			flex: 1,
			justifyContent: 'center',
			gap: theme.spacing.s10,
			paddingHorizontal: theme.spacing.s4,
			paddingVertical: theme.spacing.s4,
		},
		header: {
			gap: theme.spacing.s1,
		},
		welcomeText: {
			fontWeight: 'bold',
			color: theme.colors.white,
		},
		welcomeTextFocus: {
			color: theme.colors.primary,
		},
		body: {
			gap: theme.spacing.s2,
		},
		containerRemember: {
			flexDirection: 'row',
			justifyContent: 'space-between',
		},
		forgotPassword: {
			fontSize: theme.fontSize.small,
			color: theme.colors.textLight,
		},
		footer: {
			alignItems: 'center',
		},
		footerButton: {
			padding: theme.spacing.s2,
		},
		textFooter: {
			letterSpacing: 1,
		},
		textSignUp: {
			color: theme.colors.primary,
			fontWeight: 'semibold',
			letterSpacing: 1,
			fontSize: theme.fontSize.small,
		},
	})
}
