import { ValidationResult } from '../../../../types/auth'
import { LOGIN_VALIDATION_MESSAGES } from './login.constants'

/**
 * Tipo para funções de handlers
 */
type FormHandlers = {
	handleTextChange: (field: string) => (value: string) => void
	handleToggle: (field: string) => () => void
}

/**
 * Tipos específicos para campos do formulário de login
 */
type LoginFormField = 'email' | 'password' | 'remember' | 'passwordVisible'

/**
 * Tipo para função de atualização de campo fortemente tipada
 */
type UpdateFieldFunction = (field: LoginFormField, value: string | boolean) => void

/**
 * Tipo para função de toggle fortemente tipada
 */
type ToggleFieldFunction = (field: LoginFormField) => void

/**
 * Cria handlers genéricos para formulários
 * @param updateField - Função para atualizar campos
 * @param toggleField - Função para alternar campos boolean
 * @returns Objeto com handlers genéricos
 */
export const createFormHandlers = (
	updateField: (field: string, value: string | boolean) => void,
	toggleField: (field: string) => void
): FormHandlers => ({
	handleTextChange: (field: string) => (value: string) => updateField(field, value),
	handleToggle: (field: string) => () => toggleField(field),
})

/**
 * Cria handlers com tipagem forte para formulários de login
 * @param updateField - Função para atualizar campos tipada
 * @param toggleField - Função para alternar campos boolean tipada
 * @returns Objeto com handlers tipados
 */
export const createTypedFormHandlers = (
	updateField: UpdateFieldFunction,
	toggleField: ToggleFieldFunction
): FormHandlers => ({
	handleTextChange: (field: string) => (value: string) => {
		updateField(field as LoginFormField, value)
	},
	handleToggle: (field: string) => () => {
		toggleField(field as LoginFormField)
	},
})

/**
 * Valida os dados do formulário de login
 * @param email - Email digitado pelo usuário
 * @param password - Senha digitada pelo usuário
 * @returns Resultado da validação com isValid e message
 */
export const validateLoginForm = (email: string, password: string): ValidationResult => {
	// Trim espaços em branco e verificar se estão vazios
	const trimmedEmail = email?.trim() || ''
	const trimmedPassword = password?.trim() || ''

	if (!trimmedEmail || !trimmedPassword) {
		return {
			isValid: false,
			message: LOGIN_VALIDATION_MESSAGES.REQUIRED_FIELDS,
		}
	}

	if (!isValidEmailFormat(trimmedEmail)) {
		return {
			isValid: false,
			message: LOGIN_VALIDATION_MESSAGES.INVALID_EMAIL,
		}
	}

	return {
		isValid: true,
		message: undefined,
	}
}

/**
 * Verifica se o e-mail tem um formato válido
 * @param email - Email a ser validado
 * @returns True se válido, false caso contrário
 */
export const isValidEmailFormat = (email: string): boolean => {
	// Verificar se o email não está vazio ou só espaços
	if (!email || typeof email !== 'string' || !email.trim()) {
		return false
	}

	const trimmedEmail = email.trim()

	// Verificações básicas
	if (trimmedEmail.includes('..') || trimmedEmail.includes(' ')) {
		return false
	}

	// Regex para email válido
	const emailRegex = /^[a-zA-Z0-9][a-zA-Z0-9._%+-]*[a-zA-Z0-9]@[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]\.[a-zA-Z]{2,}$/
	return emailRegex.test(trimmedEmail)
}
