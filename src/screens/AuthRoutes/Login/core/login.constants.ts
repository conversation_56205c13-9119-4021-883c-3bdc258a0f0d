import {
	AuthApiConfig,
	AuthRoute,
	AuthRout<PERSON>,
	AuthStorageKeys,
	AuthTexts,
	AutoCapitalizeType,
	FormFieldConfig,
	KeyboardType,
} from '../../../../types/auth'

/**
 * Configurações dos campos do formulário
 * Centraliza todas as configurações dos inputs
 */
export const LOGIN_FORM_CONFIG: Record<string, FormFieldConfig> = {
	email: {
		label: 'E-mail',
		placeholder: '<EMAIL>',
		keyboardType: KeyboardType.EMAIL_ADDRESS,
		autoCapitalize: AutoCapitalizeType.NONE,
	},
	password: {
		label: 'Senha',
		placeholder: '**********',
		autoCapitalize: AutoCapitalizeType.NONE,
	},
	remember: {
		label: 'Lembrar-me',
	},
}

/**
 * Mensagens de validação do formulário
 */
export const LOGIN_VALIDATION_MESSAGES: AuthTexts = {
	REQUIRED_FIELDS: 'Preencha com e-mail e senha',
	INVALID_EMAIL: 'Digite um e-mail válido',
}

/**
 * Configurações de navegação
 */
export const LOGIN_ROUTES: AuthRoutes = {
	FORGOT_PASSWORD: AuthRoute.FORGOT_PASSWORD,
	SIGN_UP: AuthRoute.SIGN_UP,
}

/**
 * Textos estáticos da tela
 */
export const LOGIN_TEXTS: AuthTexts = {
	WELCOME_TITLE: 'SEJA BEM',
	WELCOME_FOCUS: 'VINDO',
	WELCOME_SUBTITLE: 'Faça login para acessar seus replays',
	FORGOT_PASSWORD: 'Esqueci minha senha',
	LOGIN_BUTTON: 'LOGIN',
	FOOTER_TEXT: 'AINDA NÃO POSSUI UMA CONTA? ',
	FOOTER_SIGNUP: 'CADASTRE-SE AGORA!',
}

/**
 * Configurações de armazenamento local
 */
export const LOGIN_STORAGE_KEYS: AuthStorageKeys = {
	USER_DATA: '@user',
}

/**
 * Configurações de API
 */
export const LOGIN_API_CONFIG: AuthApiConfig = {
	SECTION: 'login',
}
