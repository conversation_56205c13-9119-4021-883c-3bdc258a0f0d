import { useNavigation } from '@react-navigation/native'
import { useEffect } from 'react'

import { useFavoriteArenas } from '../../../../features'
import { deleteLocalItem, getLocalItem, setLocalItem } from '../../../../helpers/AsyncStorage'
import { ErrorMessage } from '../../../../helpers/HandleMessages'
import { useUser } from '../../../../stores'
import { User } from '../../../../types/auth'

import api from '../../../../utils/api'

import { LOGIN_ROUTES, LOGIN_STORAGE_KEYS } from './login.constants'
import { login } from './login.service'
import { useLoginStore } from './login.store'
import { createFormHandlers, validateLoginForm } from './login.utils'

type LoginNavigation = {
	navigate: (route: string) => void
}

interface UseLoginReturn {
	// States
	email: string
	password: string
	remember: boolean
	passwordVisible: boolean

	// Generic handlers
	updateField: (field: string, value: string | boolean) => void
	toggleField: (field: string) => void

	// Form handlers
	handleTextChange: (field: string) => (value: string) => void
	handleToggle: (field: string) => () => void

	// Specific actions
	handleLogin: () => Promise<void>

	// Navigation
	navigateToForgotPassword: () => void
	navigateToSignUp: () => void
}

export const useLogin = (): UseLoginReturn => {
	const { initializeFavoriteArenas } = useFavoriteArenas()
	const { setUser } = useUser()

	const navigation = useNavigation<LoginNavigation>()

	const { email, password, remember, passwordVisible, updateField, toggleField, setUserData } = useLoginStore()

	// Carregar dados salvos do usuário
	useEffect(() => {
		const loadSavedUserData = async (): Promise<void> => {
			const localUser = await getLocalItem(LOGIN_STORAGE_KEYS.USER_DATA)
			if (localUser) {
				setUserData(localUser)
			}
		}

		loadSavedUserData()
	}, [setUserData])

	const handleLogin = async (): Promise<void> => {
		const validation = validateLoginForm(email, password)
		if (!validation.isValid) return ErrorMessage({ message: validation.message })

		const data = await login(email, password)

		if (data) {
			// Salvar dados localmente se "lembrar-me" estiver marcado
			if (remember) {
				await setLocalItem(LOGIN_STORAGE_KEYS.USER_DATA, {
					email,
					password,
					token: data.access_token,
					...data,
				})
			} else {
				await deleteLocalItem(LOGIN_STORAGE_KEYS.USER_DATA)
			}

			// Configurar autorização na API
			api.defaults.headers.common.Authorization = `Bearer ${data.access_token}`

			// O usuário está na propriedade 'user' da resposta, se existir
			// Caso contrário, temos que mapear da própria resposta
			const user: User = data.user || {
				id_user: data.id_user || 0, // Assumir valor padrão se não vier
				name: data.name || 'Usuário', // Assumir valor padrão se não vier
				email, // Usar o email do formulário
				access_token: data.access_token,
				whatsapp: data.whatsapp,
			}
			setUser(user)

			// Buscar arenas favoritas após login bem-sucedido
			initializeFavoriteArenas()
		}
	}

	const navigateToForgotPassword = (): void => {
		navigation.navigate(LOGIN_ROUTES.FORGOT_PASSWORD)
	}

	const navigateToSignUp = (): void => {
		navigation.navigate(LOGIN_ROUTES.SIGN_UP)
	}

	return {
		// States
		email,
		password,
		remember,
		passwordVisible,

		// Generic handlers
		updateField,
		toggleField,

		// Form handlers
		...createFormHandlers(updateField, toggleField),

		// Specific actions
		handleLogin,

		// Navigation
		navigateToForgotPassword,
		navigateToSignUp,
	}
}
