import { View } from 'react-native'

import { Button, TextInput } from '../../../../components'

import { FORGOT_PASSWORD_FORM_CONFIG, FORGOT_PASSWORD_TEXTS } from '../core/forgotPassword.constants'
import { useForgotPassword } from '../core/forgotPassword.hook'
import { createForgotPasswordStyles } from '../core/forgotPassword.styles'

const ForgotPasswordForm: React.FC = () => {
	const { email, loading, handleTextChange, handleRecoverPassword } = useForgotPassword()

	const styles = createForgotPasswordStyles()

	return (
		<View style={styles.form}>
			<TextInput
				value={email}
				label={FORGOT_PASSWORD_FORM_CONFIG.email.label}
				placeholder={FORGOT_PASSWORD_FORM_CONFIG.email.placeholder}
				keyboardType={FORGOT_PASSWORD_FORM_CONFIG.email.keyboardType}
				autoCapitalize={FORGOT_PASSWORD_FORM_CONFIG.email.autoCapitalize}
				onChangeText={handleTextChange('email')}
			/>

			<Button label={FORGOT_PASSWORD_TEXTS.SEND_BUTTON} loading={loading} onPress={handleRecoverPassword} />
		</View>
	)
}

export default ForgotPasswordForm
