import { View } from 'react-native'

import { Text } from '../../../../components'

import { FORGOT_PASSWORD_TEXTS } from '../core/forgotPassword.constants'
import { createForgotPasswordStyles } from '../core/forgotPassword.styles'

const ForgotPasswordHeader: React.FC = () => {
	const styles = createForgotPasswordStyles()

	return (
		<View style={styles.header}>
			<Text style={styles.textHeader} size={'extraLarge'}>
				{FORGOT_PASSWORD_TEXTS.HEADER_TITLE}
			</Text>
		</View>
	)
}

export default ForgotPasswordHeader
