import { StyleSheet } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

import { useTheme } from '../../../../stores'

export const createForgotPasswordStyles = () => {
	const { theme } = useTheme()
	const insets = useSafeAreaInsets()

	return StyleSheet.create({
		container: {
			flex: 1,
			paddingTop: insets.top,
			paddingBottom: insets.bottom,
		},
		contain: {
			flex: 1,
			justifyContent: 'center',
			gap: theme.spacing.s4,
			paddingHorizontal: theme.spacing.s4,
			paddingVertical: theme.spacing.s1,
		},
		header: {
			alignItems: 'center',
			marginBottom: theme.spacing.s2,
		},
		textHeader: {
			color: theme.colors.primary,
			fontWeight: 'semibold',
			textAlign: 'center',
		},
		form: {
			gap: theme.spacing.s4,
		},
	})
}
