import { useNavigation } from '@react-navigation/native'

import { ErrorMessage, SuccessMessage } from '../../../../helpers/HandleMessages'
import { useApi } from '../../../../stores'

import { FORGOT_PASSWORD_API_CONFIG, FORGOT_PASSWORD_ROUTES, FORGOT_PASSWORD_TEXTS } from './forgotPassword.constants'
import { createFormHandlers, validateForgotPasswordForm } from './forgotPassword.utils'
import { sendRecoverPasswordEmail } from './forgotPassword.service'
import { useForgotPasswordStore } from './forgotPassword.store'

export const useForgotPassword = () => {
	const { sectionLoading } = useApi()
	const navigation = useNavigation<any>()

	const { email, updateField, resetForm } = useForgotPasswordStore()

	const handleRecoverPassword = async (): Promise<void> => {
		const validation = validateForgotPasswordForm(email)
		if (!validation.isValid) return ErrorMessage({ 
			title: 'Erro de validação',
			message: validation.message || 'Campo inválido'
		})

		const data = await sendRecoverPasswordEmail(email)

		if (data) {
			SuccessMessage({ message: data?.message || FORGOT_PASSWORD_TEXTS.SUCCESS_MESSAGE })
			resetForm()
			navigation.navigate(FORGOT_PASSWORD_ROUTES.LOGIN)
		}
	}

	const navigateToLogin = (): void => {
		navigation.navigate(FORGOT_PASSWORD_ROUTES.LOGIN)
	}

	return {
		// States
		email,
		loading: sectionLoading[FORGOT_PASSWORD_API_CONFIG.SECTION],

		// Generic handlers
		updateField,

		// Form handlers
		...createFormHandlers(updateField),

		// Specific actions
		handleRecoverPassword,

		// Navigation
		navigateToLogin,
	}
}
