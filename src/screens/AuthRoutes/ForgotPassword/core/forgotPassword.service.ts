import { useApi } from '../../../../stores'
import { FORGOT_PASSWORD_API_CONFIG } from './forgotPassword.constants'

const { callApi } = useApi.getState()

/**
 * Envia e-mail de recuperação de senha
 * @param email - Email do usuário
 * @returns Resultado da chamada de API
 */
export const sendRecoverPasswordEmail = async (email: string): Promise<any> =>
	callApi({
		method: 'GET',
		section: FORGOT_PASSWORD_API_CONFIG.SECTION,
		url: `/users/recoverPassword/${email}`,
	})
