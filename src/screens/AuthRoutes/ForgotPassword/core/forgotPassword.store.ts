import { create } from 'zustand'

interface ForgotPasswordState {
	email: string
}

interface ForgotPasswordActions {
	updateField: (field: keyof ForgotPasswordState, value: string) => void
	resetForm: () => void
}

type ForgotPasswordStore = ForgotPasswordState & ForgotPasswordActions

const initialState: ForgotPasswordState = {
	email: '',
}

export const useForgotPasswordStore = create<ForgotPasswordStore>((set) => ({
	...initialState,

	updateField: (field, value) => set({ [field]: value }),
	resetForm: () => set(initialState),
}))
