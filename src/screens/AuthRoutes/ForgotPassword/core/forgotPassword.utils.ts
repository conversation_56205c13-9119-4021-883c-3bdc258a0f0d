import { FORGOT_PASSWORD_VALIDATION_MESSAGES } from './forgotPassword.constants'

interface ValidationResult {
	isValid: boolean
	message: string | null
}

interface FormHandlers {
	handleTextChange: (field: string) => (value: string) => void
}

/**
 * Cria handlers genéricos para formulários
 * @param updateField - Função para atualizar campos
 * @returns Objeto com handlers genéricos
 */
export const createFormHandlers = (updateField: (field: string, value: string) => void): FormHandlers => ({
	handleTextChange: (field: string) => (value: string) => updateField(field, value),
})

/**
 * Valida o e-mail do formulário de recuperação de senha
 * @param email - Email digitado pelo usuário
 * @returns Resultado da validação com isValid e message
 */
export const validateForgotPasswordForm = (email: string): ValidationResult => {
	// Trim espaços em branco e verificar se está vazio
	const trimmedEmail = email?.trim() || ''

	if (!trimmedEmail) {
		return {
			isValid: false,
			message: FORGOT_PASSWORD_VALIDATION_MESSAGES.REQUIRED_EMAIL,
		}
	}

	if (!isValidEmailFormat(trimmedEmail)) {
		return {
			isValid: false,
			message: FORGOT_PASSWORD_VALIDATION_MESSAGES.INVALID_EMAIL,
		}
	}

	return {
		isValid: true,
		message: null,
	}
}

/**
 * Verifica se o e-mail tem um formato válido
 * @param email - Email a ser validado
 * @returns True se válido, false caso contrário
 */
export const isValidEmailFormat = (email: string): boolean => {
	// Verificar se o email não está vazio ou só espaços
	if (!email || !email.trim()) {
		return false
	}

	// Regex mais rigorosa para email
	// Não pode começar com ponto, não pode ter pontos duplos, etc.
	const emailRegex = /^[a-zA-Z0-9][a-zA-Z0-9._%+-]*[a-zA-Z0-9]@[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]\.[a-zA-Z]{2,}$/
	return emailRegex.test(email.trim())
}
