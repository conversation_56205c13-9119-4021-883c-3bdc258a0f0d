import { createBottomTabNavigator } from '@react-navigation/bottom-tabs'
import { NavigationContainer } from '@react-navigation/native'
import { createNativeStackNavigator } from '@react-navigation/native-stack'
import { useEffect } from 'react'
import { Platform } from 'react-native'

import * as NavigationBar from 'expo-navigation-bar'
import * as Updates from 'expo-updates'

import { Header, Icon } from '../components'
import { useAutoLogin } from '../features'
import { useTheme, useUserComputed } from '../stores'

import { Dashboard, ForgotPassword, Profile, SignUp } from '.'
import Login from './AuthRoutes/Login/Login'
import Arenas from './SignedRoutes/Clients/Arenas/Arenas'
import Block from './SignedRoutes/Clients/Block/Block'
import Replays from './SignedRoutes/Replays/Replays'
import VideoSwipe from './SignedRoutes/VideoSwipe/VideoSwipe'
import SplashScreen from './SplashScreen/SplashScreen'

const BottomTabs = createBottomTabNavigator()
const Stack = createNativeStackNavigator()

// Configuração de quais telas mostram a pesquisa
const SEARCH_ENABLED_SCREENS = ['ReplaysTab']
const BLOCK_NAVIGATION_SCREENS = ['Arenas']

const ReplayRoutes = () => {
	const { theme } = useTheme()
	return (
		<Stack.Navigator
			screenOptions={{
				headerShown: false,
				contentStyle: { backgroundColor: theme.colors.background },
			}}
		>
			<Stack.Screen name={'ReplaysList'} component={Replays} />
		</Stack.Navigator>
	)
}

const SignedRoutes = () => {
	const { theme } = useTheme()
	const { user } = useUserComputed()

	return (
		<Stack.Navigator
			screenOptions={{
				headerShown: false,
				contentStyle: { backgroundColor: theme.colors.background },
			}}
		>
			{user?.profile === 'client' ? (
				<Stack.Screen name={'Main'} component={ClientTabs} />
			) : (
				<Stack.Screen name={'Main'} component={UserStack} />
			)}

			<Stack.Screen
				name={'Profile'}
				component={Profile}
				options={{
					...(Platform.OS === 'android'
						? {
								animation: 'slide_from_bottom' as any,
							}
						: {
								presentation: 'modal',
							}),
				}}
			/>

			<Stack.Screen
				name={'VideoSwipe'}
				component={VideoSwipe as any}
				options={{
					...(Platform.OS === 'android'
						? {
								animation: 'slide_from_bottom' as any,
							}
						: {
								presentation: 'modal',
							}),
				}}
			/>
		</Stack.Navigator>
	)
}

// Stack Navigator para usuários normais
const UserStack = () => {
	const { theme } = useTheme()

	return (
		<Stack.Navigator
			initialRouteName={'ReplaysTab'}
			screenOptions={{
				header: (props) => <Header showSearch={true} {...props} />,
				contentStyle: { backgroundColor: theme.colors.background },
			}}
		>
			<Stack.Screen name={'ReplaysTab'} component={ReplayRoutes} />
			<Stack.Screen
				name={'Profile'}
				component={Profile}
				options={{
					headerShown: false,
					presentation: 'modal',
				}}
			/>
		</Stack.Navigator>
	)
}

// Bottom Tab Navigator para clientes
const ClientTabs = () => {
	const { theme } = useTheme()

	return (
		<BottomTabs.Navigator
			initialRouteName={'ReplaysTab'}
			sceneContainerStyle={{ backgroundColor: theme.colors.background }}
			screenOptions={({ route }) => ({
				header: (props) => {
					const title =
						route.name === 'Dashboard'
							? 'Dashboard'
							: route.name === 'Arenas'
								? 'Arenas'
								: route.name === 'Block'
									? 'Bloqueio de Vídeos'
									: undefined

					const showSearch = SEARCH_ENABLED_SCREENS.includes(route.name)
					const showBlock = BLOCK_NAVIGATION_SCREENS.includes(route.name)

					return <Header title={title} showSearch={showSearch} showBlock={showBlock} {...props} />
				},
				tabBarActiveTintColor: theme.colors.primary,
				tabBarInactiveTintColor: theme.colors.white,
				tabBarStyle: {
					paddingBottom: Platform.OS === 'ios' ? 10 : 0,
					backgroundColor: theme.colors.background,
					justifyContent: 'center',
				},
				tabBarShowLabel: false,
			})}
		>
			<BottomTabs.Screen
				name={'Dashboard'}
				component={Dashboard}
				options={{
					tabBarIcon: ({ color }) => (
						<Icon library={'FontAwesome'} name={'line-chart'} size={theme.spacing.s3} color={color} />
					),
				}}
			/>

			<BottomTabs.Screen
				name={'ReplaysTab'}
				component={ReplayRoutes}
				options={{
					unmountOnBlur: true,
					tabBarIcon: ({ color }) => (
						<Icon
							library={'MaterialCommunityIcons'}
							name={'motion-play-outline'}
							size={theme.spacing.s4}
							color={color}
						/>
					),
				}}
			/>

			<BottomTabs.Screen
				name={'Arenas'}
				component={Arenas}
				options={{
					tabBarIcon: ({ color }) => (
						<Icon
							library={'MaterialCommunityIcons'}
							name={'soccer-field'}
							size={theme.spacing.s4}
							color={color}
						/>
					),
				}}
			/>

			<BottomTabs.Screen
				name={'Block'}
				component={Block}
				options={{
					tabBarButton: () => null,
				}}
			/>
		</BottomTabs.Navigator>
	)
}

const AuthRoutes = () => {
	const { theme } = useTheme()
	return (
		<Stack.Navigator
			screenOptions={{
				headerShown: false,
				contentStyle: { backgroundColor: theme.colors.background },
			}}
		>
			<Stack.Screen name={'Login'} component={Login} />
			<Stack.Screen name={'ForgotPassword'} component={ForgotPassword} />
			<Stack.Screen name={'SignUp'} component={SignUp} />
		</Stack.Navigator>
	)
}

function Router() {
	const { isCheckingAuth } = useAutoLogin()
	const { signed } = useUserComputed()
	const { theme } = useTheme()

	useEffect(() => {
		;(async () => {
			// eslint-disable-next-line no-undef
			if (!__DEV__) await checkForUpdates()

			if (Platform.OS === 'android') await NavigationBar.setBackgroundColorAsync(theme.colors.background)
		})()
	}, [])

	const checkForUpdates = async (): Promise<void> => {
		try {
			const update = await Updates.checkForUpdateAsync()
			if (update.isAvailable) {
				await Updates.fetchUpdateAsync()
				await Updates.reloadAsync()
			}
		} catch (e) {
			console.error('Erro ao verificar atualização:', e)
		}
	}

	if (isCheckingAuth) {
		return <SplashScreen />
	}

	return (
		<NavigationContainer>
			<Stack.Navigator screenOptions={{ headerShown: false }}>
				{signed ? (
					<Stack.Screen name={'SignedRoutes'} component={SignedRoutes} />
				) : (
					<Stack.Screen name={'AuthRoutes'} component={AuthRoutes} />
				)}
			</Stack.Navigator>
		</NavigationContainer>
	)
}

export default Router
