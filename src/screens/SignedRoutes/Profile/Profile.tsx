import { View } from 'react-native'

import { Text } from '../../../components'

import { PROFILE_TEXTS } from './core/profile.constants'
import { createProfileStyles } from './core/profile.styles'

import ProfileFooter from './components/ProfileFooter'
import ProfileForm from './components/ProfileForm'
import ProfileHeader from './components/ProfileHeader'

export default function Profile() {
	const styles = createProfileStyles()

	return (
		<View style={{ flex: 1 }}>
			<ProfileHeader />

			<View style={styles.container}>
				<Text style={styles.title} size={'extraLarge'}>
					{PROFILE_TEXTS.TITLE}
				</Text>

				<ProfileForm />
				<ProfileFooter />
			</View>
		</View>
	)
}
