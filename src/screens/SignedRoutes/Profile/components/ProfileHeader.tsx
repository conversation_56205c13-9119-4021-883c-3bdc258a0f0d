import React from 'react'
import { View } from 'react-native'

import { Icon } from '../../../../components'

import { useProfile } from '../core/profile.hook'
import { createProfileStyles } from '../core/profile.styles'

const ProfileHeader: React.FC = () => {
	const { theme, handleGoBack } = useProfile()
	const styles = createProfileStyles()

	return (
		<View style={styles.headerContainer}>
			<Icon name={'close'} library={'AntDesign'} size={'large'} onPress={handleGoBack} />
		</View>
	)
}

export default React.memo(ProfileHeader)
