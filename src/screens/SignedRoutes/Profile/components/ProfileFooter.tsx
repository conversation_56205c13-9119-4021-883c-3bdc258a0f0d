import React from 'react'
import { View } from 'react-native'

import { Button } from '../../../../components'

import { PROFILE_TEXTS } from '../core/profile.constants'
import { useProfile } from '../core/profile.hook'
import { createProfileStyles } from '../core/profile.styles'

const ProfileFooter: React.FC = () => {
	const { theme, loading, handleSave, handleLogout } = useProfile()
	const styles = createProfileStyles()

	return (
		<View style={styles.footer}>
			<Button label={PROFILE_TEXTS.UPDATE_BUTTON} onPress={handleSave} loading={loading} />

			<Button
				label={PROFILE_TEXTS.LOGOUT_BUTTON}
				onPress={handleLogout}
				colors={[theme.colors.red, theme.colors.textLight]}
			/>
		</View>
	)
}

export default React.memo(ProfileFooter)
