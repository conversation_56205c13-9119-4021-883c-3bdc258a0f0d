import React from 'react'
import { View } from 'react-native'

import { TextInput } from '../../../../components'

import { PROFILE_FORM_CONFIG } from '../core/profile.constants'
import { useProfile } from '../core/profile.hook'
import { createProfileStyles } from '../core/profile.styles'

const ProfileForm: React.FC = () => {
	const {
		name,
		email,
		password,
		confirmPassword,
		whatsapp,
		showPassword,
		showConfirmPassword,
		theme,
		handleTextChange,
		handleToggle,
	} = useProfile()

	const styles = createProfileStyles()

	return (
		<View style={styles.contain}>
			<TextInput
				label={PROFILE_FORM_CONFIG.name.label}
				value={name}
				placeholder={PROFILE_FORM_CONFIG.name.placeholder}
				onChangeText={handleTextChange('name')}
			/>

			<TextInput
				label={PROFILE_FORM_CONFIG.email.label}
				value={email}
				placeholder={PROFILE_FORM_CONFIG.email.placeholder}
				onChangeText={handleTextChange('email')}
			/>

			<TextInput
				label={PROFILE_FORM_CONFIG.password.label}
				value={password}
				placeholder={PROFILE_FORM_CONFIG.password.placeholder}
				onChangeText={handleTextChange('password')}
				secureTextEntry={!showPassword}
				iconPosition={'right'}
				icon={{
					name: showPassword ? 'eye-off' : 'eye',
					library: 'Feather',
					color: theme.colors.text,
					size: 'small',
					onPress: handleToggle('showPassword'),
				}}
			/>

			<TextInput
				label={PROFILE_FORM_CONFIG.confirmPassword.label}
				value={confirmPassword}
				placeholder={PROFILE_FORM_CONFIG.confirmPassword.placeholder}
				onChangeText={handleTextChange('confirmPassword')}
				secureTextEntry={!showConfirmPassword}
				iconPosition={'right'}
				icon={{
					name: showConfirmPassword ? 'eye-off' : 'eye',
					library: 'Feather',
					color: theme.colors.text,
					size: 'small',
					onPress: handleToggle('showConfirmPassword'),
				}}
			/>

			<TextInput
				label={PROFILE_FORM_CONFIG.whatsapp.label}
				value={whatsapp}
				placeholder={PROFILE_FORM_CONFIG.whatsapp.placeholder}
				onChangeText={handleTextChange('whatsapp')}
				mask={'whatsapp'}
			/>
		</View>
	)
}

export default React.memo(ProfileForm)
