import { useNavigation } from '@react-navigation/native'
import { useEffect } from 'react'

import { useLogout } from '../../../../features'
import { ErrorMessage, SuccessMessage } from '../../../../helpers/HandleMessages'
import { useApi, useTheme, useUser } from '../../../../stores'

import { PROFILE_API_CONFIG, PROFILE_ERROR_MESSAGES } from './profile.constants'
import { updateProfile } from './profile.service'
import { useProfileStore } from './profile.store'
import { createFormHandlers, validateProfileForm } from './profile.utils'

export const useProfile = () => {
	const { sectionLoading } = useApi()
	const { user } = useUser()
	const { logout } = useLogout()
	const { theme } = useTheme()
	const navigation = useNavigation()

	const {
		name,
		email,
		password,
		confirmPassword,
		whatsapp,
		showPassword,
		showConfirmPassword,
		updateField,
		toggleField,
		setUserData,
		clearPasswords,
	} = useProfileStore()

	const loading = sectionLoading[PROFILE_API_CONFIG.SECTION] || false

	// Carregar dados do usuário ao inicializar
	useEffect(() => {
		if (user) {
			setUserData(user)
		}
	}, [user, setUserData])

	const handleSave = async () => {
		const validation = validateProfileForm(name, email, password, confirmPassword)
		if (!validation.isValid) {
			return ErrorMessage({ message: validation.message })
		}

		try {
			const profileData = {
				name,
				email,
				password,
				password_confirmation: confirmPassword,
				whatsapp,
			}

			const data = await updateProfile(user.id_user, profileData)
			SuccessMessage({ message: data.message })
			clearPasswords()
			// Navegação ou outras ações pós-sucesso podem ser adicionadas aqui
		} catch (error: any) {
			ErrorMessage({ message: error?.message || PROFILE_ERROR_MESSAGES.FILL_ALL_FIELDS })
		}
	}

	const handleLogout = async () => {
		await logout()
	}

	const handleGoBack = () => {
		navigation.goBack()
	}

	return {
		name,
		email,
		password,
		confirmPassword,
		whatsapp,
		showPassword,
		showConfirmPassword,
		updateField,
		toggleField,
		setUserData,
		clearPasswords,
		loading,
		handleSave,
		handleLogout,
		handleGoBack,
		logout,
		theme,
		navigation,
		...createFormHandlers(updateField, toggleField),
	}
}
