import { StyleSheet } from 'react-native'
import { useTheme } from '../../../../stores'

export const createProfileStyles = () => {
	const { theme } = useTheme()

	return StyleSheet.create({
		container: {
			gap: theme.spacing.s4,
			justifyContent: 'center',
			paddingHorizontal: theme.spacing.s4,
			paddingVertical: theme.spacing.s1,
			backgroundColor: theme.colors.background,
		},
		headerContainer: {
			padding: theme.spacing.s4,
		},
		contain: {
			gap: theme.spacing.s2,
		},
		title: {
			color: theme.colors.primary,
			fontWeight: '600' as any, // 'semibold' não é válido no RN, usar '600'
			textAlign: 'center' as const,
		},
		footer: {
			gap: theme.spacing.s2,
		},
	})
}
