import { create } from 'zustand'

interface ProfileState {
	name: string
	email: string
	password: string
	confirmPassword: string
	whatsapp: string
	showPassword: boolean
	showConfirmPassword: boolean
	updateField: (field: string, value: string) => void
	toggleField: (field: string) => void
	setUserData: (userData: any) => void
	clearPasswords: () => void
	resetForm: () => void
}

const initialState = {
	name: '',
	email: '',
	password: '',
	confirmPassword: '',
	whatsapp: '',
	showPassword: false,
	showConfirmPassword: false,
}

export const useProfileStore = create<ProfileState>((set) => ({
	...initialState,
	updateField: (field, value) => set({ [field]: value }),
	toggleField: (field) => set((state) => ({ [field]: !state[field] })),
	setUserData: (userData) =>
		set({
			name: userData.name || '',
			email: userData.email || '',
			whatsapp: userData.whatsapp || '',
		}),
	clearPasswords: () =>
		set({
			password: '',
			confirmPassword: '',
		}),
	resetForm: () => set(initialState),
}))
