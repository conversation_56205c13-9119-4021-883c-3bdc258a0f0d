export const validateProfileForm = (
	name: string,
	email: string,
	password: string,
	confirmPassword: string
): { isValid: boolean; message?: string } => {
	if (!name || !email || !password || !confirmPassword) {
		return { isValid: false, message: 'Preencha todos os campos' }
	}
	if (password !== confirmPassword) {
		return { isValid: false, message: 'As senhas não conferem' }
	}
	return { isValid: true }
}

export const formatWhatsapp = (value: string) => value.replace(/\D/g, '')

export const createFormHandlers = (
	updateField: (field: string, value: string) => void,
	toggleField: (field: string) => void
) => ({
	handleTextChange: (field: string) => (value: string) => {
		if (field === 'whatsapp') {
			updateField(field, formatWhatsapp(value))
		} else {
			updateField(field, value)
		}
	},
	handleToggle: (field: string) => () => toggleField(field),
})
