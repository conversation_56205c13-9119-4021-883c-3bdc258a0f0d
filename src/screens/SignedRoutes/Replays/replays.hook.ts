import { useCallback, useEffect, useState } from 'react'
import { useFavoriteArenas } from '../../../features'
import { useReplayVideo } from './core/replays.video.hook'

export const useReplays = () => {
	const [isReady, setIsReady] = useState(false)
	const { initializeFavoriteArenas } = useFavoriteArenas()
	const { setVideos } = useReplayVideo()

	useEffect(() => {
		const initialize = async () => {
			await initializeFavoriteArenas()
			setIsReady(true)
		}
		initialize()
	}, [initializeFavoriteArenas])

	const handleVideosAvailable = useCallback(
		(newVideos: any[]) => {
			setVideos(newVideos)
		},
		[setVideos]
	)

	return {
		isReady,
		handleVideosAvailable,
	}
}
