import { Navigation<PERSON><PERSON>, useFocusEffect, useNavigation } from '@react-navigation/native'
import { useCallback } from 'react'
import { View } from 'react-native'

import { Video } from '../../../components'
import { useTheme } from '../../../stores'
import { FilterBar } from './components/FilterBar'
import { useReplayFilters } from './core/replays.filters.hook'
import { useReplays } from './core/replays.hook'

import { VIDEO_PLAY_DELAY } from './core/replays.constants'
import { createReplaysStyles } from './core/replays.styles'

import NewFeatureTutorial from './components/NewFeatureTutorial/NewFeatureTutorial'
import VideosList from './components/VideosList/VideosList'

const Replays: React.FC = () => {
	const {
		isReady,
		handleVideosAvailable,
		selectedVideo,
		videos,
		filtersVisible,
		shouldRemeasureFilterBar,
		videoRef,
		handleSelectVideo,
		handleCameraChange,
		shareVideo,
		downloadVideo,
		toggleFilters,
		setFiltersVisible,
		setShouldRemeasureFilterBar,
	} = useReplays()

	const { isLoadingVideos } = useReplayFilters()

	const { theme } = useTheme()
	const navigation = useNavigation<NavigationProp<any>>()
	const styles = createReplaysStyles(theme)

	useFocusEffect(
		useCallback(() => {
			if (selectedVideo && videoRef?.current) {
				try {
					setTimeout(() => {
						if (videoRef.current) {
							videoRef.current.playAsync()
						}
					}, VIDEO_PLAY_DELAY)
				} catch {
					/* ignorar erro */
				}
			}

			return () => {
				if (videoRef?.current) {
					try {
						videoRef.current.pauseAsync()
					} catch {
						/* ignorar erro */
					}
				}
			}
		}, [selectedVideo, videoRef])
	)

	const navigateToVideoSwipe = () =>
		navigation.navigate('VideoSwipe' as any, {
			videos,
			selectedVideo,
		})

	return (
		<View style={styles.container}>
			<NewFeatureTutorial />

			{selectedVideo && (
				<Video
					ref={videoRef}
					url={selectedVideo.link_amazon_video}
					useNativeControls={false}
					onChangeCamera={selectedVideo.camera2 ? handleCameraChange : null}
					onShare={shareVideo}
					onDownload={downloadVideo}
					onExpand={navigateToVideoSwipe}
				/>
			)}

			{isReady && (
				<View style={styles.scrollContainer}>
					<FilterBar
						selectedVideo={selectedVideo?.id}
						onVideosAvailable={handleVideosAvailable}
						filtersVisible={filtersVisible}
						toggleFilters={toggleFilters}
						shouldRemeasureFilterBar={shouldRemeasureFilterBar}
						setShouldRemeasureFilterBar={setShouldRemeasureFilterBar}
						setFiltersVisible={setFiltersVisible}
					/>

					<VideosList
						videos={videos.map((v) => ({
							id_video: v.id_video || '',
							id_camera2: v.id_camera2 || undefined,
							link_amazon_thumb: v.link_amazon_thumb || '',
							link_amazon_video: v.link_amazon_video || '',
							link_download: v.link_download || '',
							time_video: v.time_video || '',
						}))}
						selectedVideo={selectedVideo?.id}
						onSelectVideo={(video) => {
							handleSelectVideo({
								id: video.id_video,
								time_video: video.time_video,
								link_amazon_thumb: video.link_amazon_thumb,
								link_amazon: video.link_amazon_video,
								link_amazon_video: video.link_amazon_video,
								camera2: video.id_camera2 ? { id: video.id_camera2 } : undefined,
							})
						}}
						loading={isLoadingVideos}
					/>
				</View>
			)}
		</View>
	)
}

export default Replays
