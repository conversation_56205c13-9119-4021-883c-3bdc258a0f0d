import { LinearGradient } from 'expo-linear-gradient'
import { useRef } from 'react'
import { Animated, Pressable, StyleSheet, View, ViewStyle } from 'react-native'
import Image from 'react-native-fast-image'
import { Text } from '../../../../../components/atoms'
import { Video } from '../../../../../components/molecules'
import { useTheme } from '../../../../../stores'

export interface VideoListItemProps {
	active?: boolean
	name?: string
	image?: string
	video?: any
	style?: ViewStyle
	containerStyle?: ViewStyle
	aspectRatio?: number
	onPress?: () => void
	onLongPress?: () => void
	isSelectionMode?: boolean
	[key: string]: any
}

export default function VideoListItem({
	active = false,
	name,
	image,
	video,
	style,
	containerStyle,
	aspectRatio,
	onPress,
	onLongPress,
	isSelectionMode = false,
	...args
}: VideoListItemProps) {
	const { theme } = useTheme()
	const scaleAnim = useRef(new Animated.Value(1)).current
	const styles = createStyle(theme, aspectRatio)

	const handlePressIn = () => {
		Animated.timing(scaleAnim, {
			toValue: 0.95,
			duration: 200,
			useNativeDriver: true,
		}).start()
	}

	const handlePressOut = () => {
		Animated.timing(scaleAnim, {
			toValue: 1,
			duration: 200,
			useNativeDriver: true,
		}).start()
	}

	const handleLongPress = () => {
		if (!isSelectionMode && onLongPress) {
			onLongPress()
		}
		handlePressOut()
	}

	const borderGradientStyle = {
		...styles.borderGradient,
		...(active ? { padding: 2 } : { padding: 0 }),
	}

	return (
		<LinearGradient
			colors={
				active ? [theme.colors.blue, theme.colors.primary] : [theme.colors.secondary, theme.colors.secondary]
			}
			start={[0, 0]}
			end={[1, 0]}
			style={StyleSheet.compose(borderGradientStyle, containerStyle)}
		>
			<Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
				<Pressable
					style={StyleSheet.compose(image || video ? styles.container : styles.containerNoImage, style)}
					onPress={active && !isSelectionMode ? undefined : onPress}
					onLongPress={active && !isSelectionMode ? undefined : handleLongPress}
					onPressIn={active && !isSelectionMode ? undefined : handlePressIn}
					onPressOut={active && !isSelectionMode ? undefined : handlePressOut}
					delayLongPress={500}
					{...args}
				>
					{video && typeof video === 'object' && video.link_amazon_video ? (
						<View style={styles.mediaContainer}>
							<Video
								source={{ uri: video.link_amazon_video }}
								shouldPlay={!active}
								volume={0}
								rate={2}
								useNativeControls={false}
								style={styles.video}
								resizeMode={'contain'}
							/>
						</View>
					) : (
						image && (
							<View style={styles.mediaContainer}>
								<Image
									style={styles.image}
									source={{
										uri:
											image.indexOf('http') !== -1
												? image
												: `https://api.oolhonolance.com.br/img/arenas/${image}.png`,
										priority: Image.priority.high,
									}}
								/>
							</View>
						)
					)}

					<Text style={styles.name} numberOfLines={1}>
						{name}
					</Text>
				</Pressable>
			</Animated.View>
		</LinearGradient>
	)
}

const createStyle = (theme: any, aspectRatio?: number) =>
	StyleSheet.create({
		container: {
			width: '100%',
			alignItems: 'center',
			padding: 0,
			borderRadius: theme.spacing.s1,
			backgroundColor: theme.colors.secondary,
			overflow: 'hidden',
		},
		containerNoImage: {
			paddingHorizontal: theme.spacing.s2,
			borderRadius: theme.spacing.s1,
			backgroundColor: theme.colors.secondary,
		},
		mediaContainer: {
			width: '100%',
			aspectRatio: aspectRatio || 16 / 9,
			borderTopStartRadius: theme.spacing.s1,
			borderTopEndRadius: theme.spacing.s1,
			overflow: 'hidden',
			backgroundColor: theme.colors.secondary,
		},
		image: {
			height: '100%',
		},
		video: {
			width: '100%',
			height: '100%',
		},
		name: {
			fontWeight: 'semibold',
			textAlign: 'center',
			paddingVertical: theme.spacing.s1,
		},
		borderGradient: {
			borderRadius: theme.spacing.s1,
		},
	})
