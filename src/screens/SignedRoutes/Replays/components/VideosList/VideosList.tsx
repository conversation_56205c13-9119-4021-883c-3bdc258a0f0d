import { Skeleton } from 'moti/skeleton'
import { memo } from 'react'
import { Dimensions, FlatList, View } from 'react-native'

import { Button, Text } from '../../../../../components'
import { useTheme } from '../../../../../stores'
import { calculateItemWidth } from '../../core/replays.utils'
import VideoListItem from './VideoListItem'
import { useVideosList } from './videoslist.hook'

import type { Video } from './videoslist.hook'

const SCREEN_WIDTH = Dimensions.get('window').width

interface VideosListProps {
	videos: Video[]
	selectedVideo?: string
	onSelectVideo: (video: Video) => void
	loading?: boolean
}

const VideosList = memo<VideosListProps>(({ videos, selectedVideo, onSelectVideo, loading = false }) => {
	const { theme } = useTheme()
	const {
		displayedVideos,
		aspect,
		selectedVideos,
		numColumns,
		handleLoadMore,
		handleVideoSelect,
		handleLongPress,
		handleCancelSelection,
		handleDownloadAll,
		onViewableItemsChanged,
		handleScrollEnd,
	} = useVideosList({ videos, onSelectVideo })

	const renderItem = ({ item, index }: { item: Video; index: number }) => {
		const camera2 = displayedVideos.filter((video) => video.id_video === item.id_camera2)[0]
		const isSelected = selectedVideos.some((v) => v.id_video === item.id_video)
		const isSelectionMode = selectedVideos.length > 0

		return (
			item?.id_video && (
				<VideoListItem
					key={index}
					name={item.time_video}
					image={item.link_amazon_thumb}
					active={isSelectionMode ? isSelected : selectedVideo === item.id_video}
					onPress={() => handleVideoSelect({ ...item, camera2 })}
					onLongPress={isSelectionMode ? undefined : () => handleLongPress({ ...item, camera2 })}
					containerStyle={{
						width: numColumns === 3 ? '32%' : '49%',
						marginBottom: theme.spacing.s1,
					}}
					aspectRatio={aspect}
					isSelectionMode={isSelectionMode}
				/>
			)
		)
	}

	if (loading) {
		const containerPadding = theme.spacing.s2 * 2
		const gapWidth = theme.spacing.s2
		const columnCount = numColumns || 3
		const itemWidth = calculateItemWidth(SCREEN_WIDTH, containerPadding, gapWidth, columnCount)

		return (
			<View style={{ flex: 1 }}>
				<View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
					{Array(columnCount)
						.fill(null)
						.map((_, index) => (
							<Skeleton
								key={`skeleton-row1-${index}`}
								width={itemWidth}
								height={130}
								radius={theme.spacing.s1}
								colorMode={'dark'}
							/>
						))}
				</View>
				<View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: theme.spacing.s2 }}>
					{Array(columnCount)
						.fill(null)
						.map((_, index) => (
							<Skeleton
								key={`skeleton-row2-${index}`}
								width={itemWidth}
								height={130}
								radius={theme.spacing.s1}
								colorMode={'dark'}
							/>
						))}
				</View>
			</View>
		)
	}

	if (!videos || videos.length === 0) {
		return null
	}

	return (
		<View style={{ flex: 1 }}>
			{selectedVideos.length > 0 && (
				<View
					style={{
						flexDirection: 'row',
						alignItems: 'center',
						justifyContent: 'space-between',
						padding: theme.spacing.s1,
						gap: theme.spacing.s2,
					}}
				>
					<Text>{selectedVideos.length > 1 ? `${selectedVideos.length} selecionados` : '1 selecionado'}</Text>

					<View style={{ flexDirection: 'row', gap: theme.spacing.s2 }}>
						<Button
							type={'outlined'}
							icon={{ library: 'AntDesign', name: 'download', color: theme.colors.text }}
							onPress={handleDownloadAll}
							style={{ width: 40, height: 40, paddingVertical: 0, paddingHorizontal: 0 }}
						/>

						<Button
							type={'outlined'}
							icon={{ library: 'AntDesign', name: 'close', color: theme.colors.text }}
							onPress={handleCancelSelection}
							style={{ width: 40, height: 40, paddingVertical: 0, paddingHorizontal: 0 }}
						/>
					</View>
				</View>
			)}

			<FlatList
				key={`flatlist-${numColumns}-columns`}
				data={displayedVideos}
				renderItem={renderItem}
				keyExtractor={(item, index) => `${item?.id_video || ''}-${index}`}
				numColumns={numColumns}
				columnWrapperStyle={{
					flexDirection: 'row',
					gap: theme.spacing.s1,
				}}
				onEndReached={handleLoadMore}
				onEndReachedThreshold={0.5}
				onViewableItemsChanged={onViewableItemsChanged}
				onScrollEndDrag={handleScrollEnd}
				onMomentumScrollEnd={handleScrollEnd}
				removeClippedSubviews={true}
			/>
		</View>
	)
})

VideosList.displayName = 'VideosList'

export default VideosList
