import { useEffect, useState } from 'react'
import { downloadFile } from '../../../../../helpers/DownloadFile'
import { DEFAULT_ASPECT_RATIO, DEFAULT_COLUMNS, ITEMS_PER_PAGE } from '../../core/replays.constants'
import { calculateAspectRatio, getColumnsFromAspectRatio } from '../../core/replays.utils'

export interface Video {
	id_video: string
	link_amazon_thumb?: string
	link_amazon_video?: string
	[key: string]: any
}

export interface UseVideosListProps {
	videos: Video[]
	onSelectVideo: (video: Video) => void
}

export interface UseVideosListReturn {
	displayedVideos: Video[]
	aspect: number
	selectedVideos: Video[]
	numColumns: number
	visibleItems: number[]
	handleLoadMore: () => void
	handleVideoSelect: (video: Video) => void
	handleLongPress: (video: Video) => void
	handleCancelSelection: () => void
	handleDownloadAll: () => void
	onViewableItemsChanged: (info: { viewableItems: { index: number }[] }) => void
	handleScrollEnd: () => void
}

export const useVideosList = ({ videos, onSelectVideo }: UseVideosListProps): UseVideosListReturn => {
	const [tempItems, setTempItems] = useState<number[]>([])
	const [visibleItems, setVisibleItems] = useState<number[]>([0, 1, 2, 3, 4, 5, 6, 7])
	const [currentPage, setCurrentPage] = useState(0)
	const [displayedVideos, setDisplayedVideos] = useState<Video[]>([])
	const [aspect, setAspect] = useState<number>(DEFAULT_ASPECT_RATIO)
	const [selectedVideos, setSelectedVideos] = useState<Video[]>([])
	const [numColumns, setNumColumns] = useState<number>(DEFAULT_COLUMNS)

	useEffect(() => {
		;(async () => {
			if (videos && videos.length > 0 && videos[0]?.link_amazon_thumb) {
				try {
					const aspectRatio = await calculateAspectRatio(videos[0].link_amazon_thumb)
					setAspect(aspectRatio)
					setNumColumns(getColumnsFromAspectRatio(aspectRatio))
				} catch (error) {
					console.error('Erro ao calcular aspect ratio:', error)
					setAspect(16 / 9)
					setNumColumns(2)
				}
			}
		})()
	}, [videos])

	useEffect(() => {
		if (videos && Array.isArray(videos)) {
			const newVideos = videos.slice(0, (currentPage + 1) * ITEMS_PER_PAGE)
			setDisplayedVideos(newVideos)
		} else {
			setDisplayedVideos([])
		}
	}, [currentPage, videos])

	const handleLoadMore = () => {
		if (displayedVideos.length < videos.length) {
			setCurrentPage((prev) => prev + 1)
		}
	}

	const handleVideoSelect = (video: Video) => {
		if (selectedVideos.length > 0) {
			const newSelection = selectedVideos.filter((v) => v.id_video !== video.id_video)
			if (newSelection.length === selectedVideos.length) {
				setSelectedVideos([...selectedVideos, video])
			} else {
				setSelectedVideos(newSelection.length ? newSelection : [])
			}
		} else {
			onSelectVideo(video)
		}
	}

	const handleLongPress = (video: Video) => {
		setSelectedVideos([video])
	}

	const handleCancelSelection = () => {
		setSelectedVideos([])
		setDisplayedVideos([...displayedVideos])
	}

	const handleDownloadAll = () => {
		downloadFile(selectedVideos.map((video) => video.link_amazon_video))
		setSelectedVideos([])
	}

	const onViewableItemsChanged = ({ viewableItems }: { viewableItems: { index: number }[] }) => {
		const visibleIndices = viewableItems.map((item) => item.index)
		setTempItems(visibleIndices)
	}

	const handleScrollEnd = () => {
		setVisibleItems(tempItems)
	}

	return {
		displayedVideos,
		aspect,
		selectedVideos,
		numColumns,
		visibleItems,
		handleLoadMore,
		handleVideoSelect,
		handleLongPress,
		handleCancelSelection,
		handleDownloadAll,
		onViewableItemsChanged,
		handleScrollEnd,
	}
}
