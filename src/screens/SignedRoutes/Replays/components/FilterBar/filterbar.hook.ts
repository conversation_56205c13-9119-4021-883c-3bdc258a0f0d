import { useCallback, useMemo } from 'react'
import { useHeaderStore } from '../../../../../components/organisms/Header/header.store'
import { useFavoriteArenas } from '../../../../../features'
import { useReplayFilters } from '../../core/replays.filters.hook'

interface UseFilterBarProps {
	filtersVisible: boolean
	selectedVideo?: string
	shouldRemeasureFilterBar: boolean
	setShouldRemeasureFilterBar: (value: boolean) => void
	setFiltersVisible: (value: boolean) => void
	onVideosAvailable?: (videos: any[]) => void
}

export const useFilterBar = (props: UseFilterBarProps) => {
	const {
		filtersVisible,
		selectedVideo,
		shouldRemeasureFilterBar,
		setShouldRemeasureFilterBar,
		setFiltersVisible,
		onVideosAvailable,
	} = props

	const { favoriteArenas } = useFavoriteArenas()
	const filtersHook = useReplayFilters()

	// Lógica específica do FilterBar: exibição de arenas
	const getAllArenas = useCallback(() => useHeaderStore.getState().allArenas, [])

	const displayArenas = useMemo(() => {
		if (!filtersHook.selectedArena) return favoriteArenas
		const isSelectedFavorite = favoriteArenas.some((arena: any) => arena.id_arena === filtersHook.selectedArena)
		if (isSelectedFavorite) return favoriteArenas

		const allArenas = getAllArenas()
		const selectedArenaData = allArenas.find((arena: any) => arena.id_arena === filtersHook.selectedArena)
		if (selectedArenaData) return [selectedArenaData, ...favoriteArenas]
		return favoriteArenas
	}, [filtersHook.selectedArena, favoriteArenas, getAllArenas])

	// Lógica específica do FilterBar: visibilidade do conteúdo
	const isContentVisible = (displayArenas.length > 0 && !selectedVideo) || (selectedVideo && filtersVisible)

	// Wrappers para incluir callback onVideosAvailable
	const updateSelectedArena = useCallback(
		async (arenaId: string) => {
			const videos = await filtersHook.updateSelectedArena(arenaId)
			if (videos && onVideosAvailable) {
				onVideosAvailable(videos)
			}
		},
		[filtersHook, onVideosAvailable]
	)

	const updateSelectedField = useCallback(
		async (fieldId: string) => {
			const videos = await filtersHook.updateSelectedField(fieldId)
			if (videos && onVideosAvailable) {
				onVideosAvailable(videos)
			}
		},
		[filtersHook, onVideosAvailable]
	)

	const updateSelectedDay = useCallback(
		async (dayId: string) => {
			const videos = await filtersHook.updateSelectedDay(dayId)
			if (videos && onVideosAvailable) {
				onVideosAvailable(videos)
			}
		},
		[filtersHook, onVideosAvailable]
	)

	const updateSelectedHour = useCallback(
		async (hourId: string) => {
			const videos = await filtersHook.updateSelectedHour(hourId)
			if (videos && onVideosAvailable) {
				onVideosAvailable(videos)
			}
		},
		[filtersHook, onVideosAvailable]
	)

	return {
		// Estados dos filtros (reexportados do hook genérico)
		selectedArena: filtersHook.selectedArena,
		fields: filtersHook.fields,
		selectedField: filtersHook.selectedField,
		days: filtersHook.days,
		selectedDay: filtersHook.selectedDay,
		hours: filtersHook.hours,
		selectedHour: filtersHook.selectedHour,
		isLoadingFields: filtersHook.isLoadingFields,
		isLoadingDays: filtersHook.isLoadingDays,
		isLoadingHours: filtersHook.isLoadingHours,

		// Estados específicos do FilterBar
		filtersVisible,
		selectedVideo,
		isContentVisible,
		shouldRemeasureFilterBar,
		favoriteArenas,
		displayArenas,

		// Actions específicas do FilterBar
		setShouldRemeasureFilterBar,
		setFiltersVisible,

		// Actions dos filtros com callback
		updateSelectedArena,
		updateSelectedField,
		updateSelectedDay,
		updateSelectedHour,
	}
}
