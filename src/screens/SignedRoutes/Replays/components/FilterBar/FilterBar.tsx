import React, { useCallback, useEffect, useRef, useState } from 'react'
import { StyleSheet, TouchableOpacity, View } from 'react-native'
import Animated, { Easing, runOnJS, useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated'

import { useTheme } from '../../../../../stores'
import { useFilterBar } from './filterbar.hook'

import Icon from '../../../../../components/atoms/Icon/Icon'
import Text from '../../../../../components/atoms/Text/Text'
import ScrollHorizontal from '../../../../../components/organisms/ScrollHorizontal/ScrollHorizontal'

const ANIMATION_DURATION = 250

interface FilterBarProps {
	onVideosAvailable?: (videos: any[]) => void
	onRemoveArenaFavorite?: (id: string) => void
	selectedVideo?: string
	filtersVisible: boolean
	toggleFilters: (visible: boolean) => void
	shouldRemeasureFilterBar: boolean
	setShouldRemeasureFilterBar: (value: boolean) => void
	setFiltersVisible: (value: boolean) => void
}

const FilterBar: React.FC<FilterBarProps> = ({
	onVideosAvailable,
	onRemoveArenaFavorite,
	selectedVideo,
	filtersVisible,
	toggleFilters,
	shouldRemeasureFilterBar,
	setShouldRemeasureFilterBar,
	setFiltersVisible,
}) => {
	const {
		// Estados dos filtros vindos do hook
		selectedArena,
		fields,
		selectedField,
		days,
		selectedDay,
		hours,
		selectedHour,
		updateSelectedArena,
		updateSelectedField,
		updateSelectedDay,
		updateSelectedHour,
		isLoadingFields,
		isLoadingDays,
		isLoadingHours,
		// Estados locais
		isContentVisible,
		favoriteArenas,
		displayArenas,
	} = useFilterBar({
		filtersVisible,
		selectedVideo,
		shouldRemeasureFilterBar,
		setShouldRemeasureFilterBar,
		setFiltersVisible,
		onVideosAvailable,
	})

	const contentRef = useRef<View>(null)
	const [contentHeight, setContentHeight] = useState(0)
	const [isAnimating, setIsAnimating] = useState(false)

	const animatedHeight = useSharedValue(filtersVisible ? 1 : 0)
	const animatedRotate = useSharedValue(filtersVisible ? 180 : 0)

	const { theme } = useTheme()
	const styles = createStyle(theme)

	const measureContentHeight = useCallback(() => {
		if (contentRef.current && !isAnimating) {
			contentRef.current.measure((x, y, width, height) => {
				if (height > 0) {
					if (height !== contentHeight || contentHeight === 0) {
						setContentHeight(height)
					}
				}
			})
		}
	}, [contentHeight, isAnimating])

	useEffect(() => {
		const shouldShowContent = (displayArenas.length > 0 && !selectedVideo) || (selectedVideo && filtersVisible)
		if (contentRef.current && shouldShowContent && !isAnimating) {
			const timeoutId = setTimeout(measureContentHeight, 100)
			return () => clearTimeout(timeoutId)
		}
	}, [filtersVisible, selectedVideo, fields, days, hours, displayArenas, measureContentHeight, isAnimating])

	useEffect(() => {
		if (filtersVisible && shouldRemeasureFilterBar && !isAnimating) {
			const timeoutId = setTimeout(() => {
				measureContentHeight()
				setShouldRemeasureFilterBar(false)
			}, 200)
			return () => clearTimeout(timeoutId)
		}
	}, [shouldRemeasureFilterBar, filtersVisible, measureContentHeight, setShouldRemeasureFilterBar, isAnimating])

	useEffect(() => {
		if ((isLoadingFields || isLoadingDays || isLoadingHours) && filtersVisible && !isAnimating) {
			const timeoutId = setTimeout(measureContentHeight, 50)
			return () => clearTimeout(timeoutId)
		}
	}, [isLoadingFields, isLoadingDays, isLoadingHours, filtersVisible, measureContentHeight, isAnimating])

	useEffect(() => {
		if (displayArenas.length > 0) {
			const timeoutId = setTimeout(() => {
				if (contentRef.current) {
					setContentHeight(0)
					requestAnimationFrame(() => {
						contentRef.current.measure((x, y, width, height) => {
							if (height > 0) {
								setContentHeight(height)
								animatedHeight.value = withTiming(1, {
									duration: 200,
									easing: Easing.out(Easing.cubic),
								})
							}
						})
					})
				}
			}, 300)
			return () => clearTimeout(timeoutId)
		}
	}, [displayArenas.length, animatedHeight])

	useEffect(() => {
		if (filtersVisible && contentHeight > 0 && !isAnimating) {
			animatedHeight.value = withTiming(1, {
				duration: 200,
				easing: Easing.out(Easing.cubic),
			})
		}
	}, [contentHeight, filtersVisible, isAnimating, animatedHeight])

	useEffect(() => {
		if (isAnimating) return
		const shouldShowContent =
			(displayArenas.length > 0 && !selectedVideo) ||
			(selectedArena && !selectedVideo) ||
			(selectedVideo && filtersVisible)
		if (shouldShowContent !== isContentVisible) {
			setIsAnimating(true)
			if (shouldShowContent) {
				setTimeout(() => {
					animatedHeight.value = withTiming(
						1,
						{
							duration: ANIMATION_DURATION,
							easing: Easing.out(Easing.cubic),
						},
						() => {
							runOnJS(setIsAnimating)(false)
						}
					)
					if (selectedVideo) {
						animatedRotate.value = withTiming(180, {
							duration: ANIMATION_DURATION,
							easing: Easing.inOut(Easing.cubic),
						})
					}
				}, 10)
			} else {
				animatedHeight.value = withTiming(
					0,
					{
						duration: ANIMATION_DURATION,
						easing: Easing.in(Easing.cubic),
					},
					() => {
						runOnJS(setIsAnimating)(false)
					}
				)
				if (selectedVideo) {
					animatedRotate.value = withTiming(0, {
						duration: ANIMATION_DURATION,
						easing: Easing.inOut(Easing.cubic),
					})
				}
			}
		}
	}, [
		filtersVisible,
		displayArenas.length,
		selectedVideo,
		selectedArena,
		isContentVisible,
		isAnimating,
		animatedHeight,
		animatedRotate,
	])

	const animatedContainerStyle = useAnimatedStyle(
		() => ({
			opacity: animatedHeight.value,
			maxHeight: animatedHeight.value * (contentHeight > 0 ? contentHeight : 400),
			overflow: 'hidden',
		}),
		[contentHeight]
	)

	const animatedChevronStyle = useAnimatedStyle(() => ({
		transform: [{ rotate: `${animatedRotate.value}deg` }],
	}))

	const renderToggleButton = () => (
		<TouchableOpacity
			onPress={() => {
				if (!isAnimating) {
					toggleFilters(!filtersVisible)
				}
			}}
			style={styles.filterContainer}
			activeOpacity={0.7}
			disabled={isAnimating}
		>
			<Text size={'large'} style={{ fontWeight: 'semibold' }}>
				{filtersVisible ? 'Esconder filtros' : 'Mostrar filtros'}
			</Text>
			<Animated.View style={animatedChevronStyle}>
				<Icon name={'chevron-down'} library={'Feather'} size={'large'} color={theme.colors.white} />
			</Animated.View>
		</TouchableOpacity>
	)

	return (
		<View>
			{selectedVideo && renderToggleButton()}
			{((displayArenas.length > 0 && !selectedVideo) ||
				(selectedArena && !selectedVideo) ||
				(selectedVideo && isContentVisible)) && (
				<Animated.View style={[styles.animatedContainer, animatedContainerStyle]}>
					<View ref={contentRef} style={styles.content} collapsable={false}>
						{displayArenas.length > 0 ? (
							<ScrollHorizontal
								items={displayArenas}
								onPress={updateSelectedArena}
								active={selectedArena}
								idxName={'arena'}
								idxItem={'id_arena'}
								idxImage={'id_arena'}
								imageStyle={{ width: 60, height: 60 }}
								style={{ width: 180, padding: theme.spacing.s1 }}
								onPressIcone={onRemoveArenaFavorite}
								icon={{
									library: 'MaterialIcons',
									name: 'star',
									color: theme.colors.yellow,
									size: 22,
									style: { position: 'absolute', top: 0, right: 0, padding: 5 },
								}}
								favoriteItems={favoriteArenas}
								favoriteIdxItem={'id_arena'}
							/>
						) : (
							<Text style={{ textAlign: 'center', color: theme.colors.textLight }} size={'small'}>
								{'Nenhuma arena disponível'}
							</Text>
						)}
						{selectedArena && (
							<ScrollHorizontal
								items={fields}
								active={selectedField}
								onPress={updateSelectedField}
								idxName={'court'}
								idxItem={'id_court'}
								loading={isLoadingFields || (selectedArena && fields.length === 0)}
							/>
						)}
						{selectedField && (
							<ScrollHorizontal
								items={days}
								active={selectedDay}
								onPress={updateSelectedDay}
								idxName={'day'}
								idxItem={'id_video_day'}
								loading={isLoadingDays}
							/>
						)}
						{selectedDay && (
							<ScrollHorizontal
								items={hours}
								active={selectedHour}
								onPress={updateSelectedHour}
								idxName={'hour'}
								idxItem={'id_video_hour'}
								loading={isLoadingHours}
							/>
						)}
					</View>
				</Animated.View>
			)}
		</View>
	)
}

const createStyle = (theme: any) =>
	StyleSheet.create({
		animatedContainer: {
			backgroundColor: theme.colors.tertiary,
			borderRadius: theme.spacing.s1,
			overflow: 'hidden',
		},
		content: {
			gap: theme.spacing.s1,
			padding: theme.spacing.s1,
		},
		filterContainer: {
			flexDirection: 'row',
			alignItems: 'center',
			justifyContent: 'space-between',
			paddingVertical: theme.spacing.s1,
		},
	})

export default FilterBar
