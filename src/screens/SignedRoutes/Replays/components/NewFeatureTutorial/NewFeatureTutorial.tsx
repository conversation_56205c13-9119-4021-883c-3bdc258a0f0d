import LottieView from 'lottie-react-native'
import React from 'react'

import { TUTORIAL_STORAGE_KEY } from '../../core/replays.constants'
import { useTutorial } from './tutorial.hook'
import MultiDownloadTutorial from '../../../../../utils/lotties/MultiDownloadTutorial.json'
import StoryModeTutorial from '../../../../../utils/lotties/StoryModeTutorial.json'
import NavigationOnBoarding, { NavigationOnBoardingPage } from './components/NavigationOnBoarding/NavigationOnBoarding'
import WhatsNew from './components/WhatsNew/WhatsNew'

const NewFeatureTutorial: React.FC = () => {
	const { showTutorial, handleTutorialDone } = useTutorial()

	if (!showTutorial) return null

	const pages: NavigationOnBoardingPage[] = [
		{
			body: <WhatsNew />,
			subtitle: null,
			title: null,
		},
		{
			body: <LottieView source={MultiDownloadTutorial} autoPlay loop style={{ width: '100%', height: 400 }} />,
			title: 'Baixe Vários Replays',
			subtitle: 'Toque e segure para selecionar vídeos!\nUse o botão para baixar tudo de uma vez.',
		},
		{
			body: <LottieView source={StoryModeTutorial} autoPlay loop style={{ width: '100%', height: 400 }} />,
			title: 'Entre no Modo História',
			subtitle: 'Veja seus replays como storys!\nDescubra novos modos de interação com os vídeos.',
		},
	]

	const onDone = () => {
		if (TUTORIAL_STORAGE_KEY) {
			handleTutorialDone(TUTORIAL_STORAGE_KEY)
		} else {
			console.error('[ERROR] TUTORIAL_STORAGE_KEY is undefined')
		}
	}

	return <NavigationOnBoarding pages={pages} isAbsolute={true} onDone={onDone} />
}

export default NewFeatureTutorial
