import { useCallback, useState } from 'react'
import { getLocalItem, setLocalItem } from '../../../../../helpers/AsyncStorage'

/**
 * Hook específico para gerenciar tutorial
 * RESPONSABILIDADE: Apenas lógica de tutorial e storage
 * PODE SER REUTILIZADO: Em qualquer component que precise de tutorial
 */
export const useTutorial = () => {
	const [showTutorial, setShowTutorial] = useState(false)

	/**
	 * Verifica se deve mostrar o tutorial baseado no storage
	 */
	const checkTutorial = useCallback(async (storageKey: string) => {
		try {
			const hasShownTutorial = await getLocalItem(storageKey)
			// Se não encontrar no storage, significa que deve mostrar o tutorial
			const shouldShowTutorial = !hasShownTutorial
			setShowTutorial(shouldShowTutorial)
			return shouldShowTutorial
		} catch {
			// Em caso de erro, não mostrar tutorial
			setShowTutorial(false)
			return false
		}
	}, [])

	/**
	 * Marca o tutorial como finalizado
	 */
	const handleTutorialDone = useCallback(async (storageKey: string) => {
		try {
			await setLocalItem(storageKey, true)
			setShowTutorial(false)
		} catch {
			// Ignorar erro se não conseguir salvar
			setShowTutorial(false)
		}
	}, [])

	/**
	 * Força a exibição do tutorial (para testes ou reset)
	 */
	const showTutorialManually = useCallback(() => {
		setShowTutorial(true)
	}, [])

	/**
	 * Esconde o tutorial sem salvar no storage
	 */
	const hideTutorial = useCallback(() => {
		setShowTutorial(false)
	}, [])

	return {
		// Estados
		showTutorial,

		// Ações
		checkTutorial,
		handleTutorialDone,
		showTutorialManually,
		hideTutorial,
		setShowTutorial,
	}
}
