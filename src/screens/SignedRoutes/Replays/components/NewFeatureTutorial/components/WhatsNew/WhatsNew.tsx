import { StyleSheet, View } from 'react-native'
import { Icon, Text } from '../../../../../../../components'
import { useTheme } from '../../../../../../../stores'

type IconLibrary =
	| 'Feather'
	| 'FontAwesome'
	| 'Fontisto'
	| 'AntDesign'
	| 'Entypo'
	| 'EvilIcons'
	| 'Ionicons'
	| 'FontAwesome5'
	| 'FontAwesome6'
	| 'MaterialCommunityIcons'
	| 'MaterialIcons'
	| 'SimpleLineIcons'
	| 'Octicons'
	| 'default'

interface FeatureItemProps {
	icon: { library?: IconLibrary; name: string }
	title: string
	description: string
}

const WhatsNew = () => {
	const { theme } = useTheme()
	const styles = createStyle(theme)

	return (
		<View style={styles.container}>
			<View style={styles.headerContainer}>
				<Text style={styles.headerText}>O que há de novo em</Text>
				<Text style={[styles.headerText, { color: theme.colors.primary }]}>O<PERSON><PERSON> no <PERSON></Text>
			</View>

			<View style={styles.featuresContainer}>
				<FeatureItem
					icon={{ library: 'Feather', name: 'download-cloud' }}
					title={'Download Múltiplo'}
					description={
						'Agora você pode selecionar vários replays ao mesmo tempo! Toque e segure em um vídeo para iniciar a seleção e use o botão para baixar tudo de uma vez.'
					}
				/>

				<FeatureItem
					icon={{ library: 'Entypo', name: 'resize-full-screen' }}
					title={'Replays em Modo Tela Cheia'}
					description={
						'Veja seus replays em tela cheia no estilo "story"! Entre no modo de visualização e clique nos cantos para navegar entre os replays.'
					}
				/>
			</View>
		</View>
	)
}

const FeatureItem = ({ icon, title, description }: FeatureItemProps) => {
	const { theme } = useTheme()
	const styles = createStyle(theme)

	return (
		<View style={styles.featureItem}>
			<Icon library={icon.library ?? 'Feather'} name={icon.name} size={'large'} color={theme.colors.primary} />

			<View style={styles.featureHeader}>
				<Text style={styles.featureTitle}>{title}</Text>
				<Text style={styles.featureDescription}>{description}</Text>
			</View>
		</View>
	)
}

const createStyle = (theme: any) =>
	StyleSheet.create({
		container: {
			padding: theme.spacing.s3,
			gap: theme.spacing.s6,
			justifyContent: 'center',
		},
		headerContainer: {
			alignItems: 'center',
		},
		headerText: {
			fontSize: 28,
			fontWeight: 'bold',
			color: theme.colors.text,
		},
		featuresContainer: {
			gap: theme.spacing.s2,
		},
		featureItem: {
			flexDirection: 'row',
			alignItems: 'center',
			backgroundColor: theme.colors.secondary,
			padding: theme.spacing.s2,
			borderRadius: theme.spacing.s2,
			gap: theme.spacing.s3,
		},
		featureHeader: {
			flex: 1,
			gap: theme.spacing.s1,
		},
		featureTitle: {
			fontSize: theme.fontSize.large,
			fontWeight: 'bold',
			color: theme.colors.text,
		},
		featureDescription: {
			fontSize: theme.fontSize.medium,
			lineHeight: theme.spacing.s3,
			color: theme.colors.textLight,
			flexShrink: 1,
			flexWrap: 'wrap',
		},
	})

export default WhatsNew
