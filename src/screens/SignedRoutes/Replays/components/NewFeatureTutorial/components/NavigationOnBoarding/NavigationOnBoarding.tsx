import React, { useRef, useState } from 'react'
import { Animated, Dimensions, FlatList, StyleSheet, View } from 'react-native'
import {
	GestureHandlerRootView,
	PanGestureHandler,
	PanGestureHandlerStateChangeEvent,
	State,
} from 'react-native-gesture-handler'
import { Button } from '../../../../../../../components'
import { useTheme } from '../../../../../../../stores'

const { width } = Dimensions.get('window')

export interface NavigationOnBoardingPage {
	body: React.ReactNode
	title?: string | null
	subtitle?: string | null
}

interface NavigationOnBoardingProps {
	pages: NavigationOnBoardingPage[]
	onDone?: () => void
	isAbsolute?: boolean
}

const NavigationOnBoarding: React.FC<NavigationOnBoardingProps> = ({ pages, onDone, isAbsolute = false }) => {
	const [currentIndex, setCurrentIndex] = useState(0)
	const { theme } = useTheme()
	const flatListRef = useRef<FlatList<any>>(null)
	const scrollX = useRef(new Animated.Value(0)).current
	const styles = createStyle(theme)
	const translateX = useRef(new Animated.Value(0)).current

	const renderSlideContent = ({ item }: { item: NavigationOnBoardingPage }) => (
		<Animated.View
			style={[
				styles.slide,
				{
					width,
					transform: [
						{
							translateX: translateX.interpolate({
								inputRange: [-width, 0, width],
								outputRange: [-width, 0, width],
								extrapolate: 'clamp',
							}),
						},
					],
				},
			]}
		>
			{item.body}
			{(item.title || item.subtitle) && (
				<View style={styles.textContainer}>
					{item.title && (
						<Animated.Text style={[styles.title, { color: theme.colors.text }]}>{item.title}</Animated.Text>
					)}
					{item.subtitle && (
						<Animated.Text style={[styles.subtitle, { color: theme.colors.text }]}>
							{' '}
							{item.subtitle}{' '}
						</Animated.Text>
					)}
				</View>
			)}
		</Animated.View>
	)

	const handleNext = () => {
		if (currentIndex < pages.length - 1) {
			flatListRef.current?.scrollToIndex({
				index: currentIndex + 1,
				animated: true,
			})
		}
	}

	const handleDone = () => {
		onDone?.()
	}

	const renderPagination = () => (
		<View style={styles.paginationContainer}>
			<View style={styles.paginationDots}>
				{pages.map((_, index) => {
					const inputRange = [(index - 1) * width, index * width, (index + 1) * width]

					const dotWidth = scrollX.interpolate({
						inputRange,
						outputRange: [8, 16, 8],
						extrapolate: 'clamp',
					})

					const opacity = scrollX.interpolate({
						inputRange,
						outputRange: [0.3, 1, 0.3],
						extrapolate: 'clamp',
					})

					return (
						<Animated.View
							key={index}
							style={[
								styles.dot,
								{
									width: dotWidth,
									opacity,
									backgroundColor: theme.colors.primary,
								},
							]}
						/>
					)
				})}
			</View>

			{currentIndex < pages.length - 1 ? (
				<Button
					icon={{
						library: 'Feather',
						name: 'chevron-right',
						color: theme.colors.text,
						size: 'large',
					}}
					size={'small'}
					color={theme.colors.primary}
					style={styles.navigationButton}
					onPress={handleNext}
				/>
			) : (
				<Button
					icon={{
						library: 'Ionicons',
						name: 'checkmark',
						color: theme.colors.text,
						size: 'large',
					}}
					color={theme.colors.primary}
					style={styles.navigationButton}
					onPress={handleDone}
				/>
			)}
		</View>
	)

	const onViewableItemsChanged = useRef(({ viewableItems }: { viewableItems: { index: number }[] }) => {
		if (viewableItems[0]) {
			setCurrentIndex(viewableItems[0].index)
		}
	}).current

	const viewabilityConfig = useRef({
		itemVisiblePercentThreshold: 50,
	}).current

	const onGestureEvent = Animated.event([{ nativeEvent: { translationX: translateX } }], { useNativeDriver: true })

	const onHandlerStateChange = ({ nativeEvent }: PanGestureHandlerStateChangeEvent) => {
		if (nativeEvent.oldState === State.ACTIVE) {
			const { translationX } = nativeEvent
			if (Math.abs(translationX) > width * 0.2) {
				const newIndex =
					translationX > 0 ? Math.max(0, currentIndex - 1) : Math.min(pages.length - 1, currentIndex + 1)
				flatListRef.current?.scrollToIndex({
					index: newIndex,
					animated: true,
				})
			} else {
				flatListRef.current?.scrollToIndex({
					index: currentIndex,
					animated: true,
				})
			}
			translateX.setValue(0)
		}
	}

	const containerStyle = isAbsolute ? styles.absoluteContainer : styles.container

	return (
		<GestureHandlerRootView style={containerStyle}>
			<View style={styles.container}>
				<PanGestureHandler
					onGestureEvent={onGestureEvent}
					onHandlerStateChange={onHandlerStateChange}
					activeOffsetX={[-10, 10]}
				>
					<Animated.View style={styles.slideContainer}>
						<FlatList
							ref={flatListRef}
							data={pages}
							renderItem={renderSlideContent}
							pagingEnabled
							horizontal
							showsHorizontalScrollIndicator={false}
							onScroll={Animated.event([{ nativeEvent: { contentOffset: { x: scrollX } } }], {
								useNativeDriver: false,
							})}
							onViewableItemsChanged={onViewableItemsChanged}
							viewabilityConfig={viewabilityConfig}
							scrollEventThrottle={1}
							decelerationRate={0.992}
							snapToInterval={width}
							snapToAlignment="center"
							bounces={false}
							overScrollMode="never"
						/>
					</Animated.View>
				</PanGestureHandler>
				{renderPagination()}
			</View>
		</GestureHandlerRootView>
	)
}

const createStyle = (theme: any) =>
	StyleSheet.create({
		container: {
			flex: 1,
		},
		absoluteContainer: {
			position: 'absolute',
			top: 0,
			left: 0,
			right: 0,
			bottom: 0,
			zIndex: 999,
			backgroundColor: theme.colors.background,
		},
		slide: {
			flex: 1,
			justifyContent: 'center',
		},
		textContainer: {
			alignItems: 'center',
			paddingHorizontal: 20,
			marginTop: 20,
		},
		title: {
			fontSize: 24,
			fontWeight: 'bold',
			marginBottom: 10,
			textAlign: 'center',
		},
		subtitle: {
			fontSize: 16,
			textAlign: 'center',
		},
		paginationContainer: {
			position: 'absolute',
			bottom: 40,
			left: 20,
			right: 20,
			flexDirection: 'row',
			justifyContent: 'space-between',
			alignItems: 'center',
		},
		paginationDots: {
			flexDirection: 'row',
			alignItems: 'center',
		},
		dot: {
			height: 8,
			borderRadius: 4,
			marginHorizontal: 4,
		},
		navigationButton: {
			width: 40,
			height: 40,
			borderRadius: 40,
			justifyContent: 'center',
			alignItems: 'center',
			paddingVertical: 0,
		},
		slideContainer: {
			flex: 1,
			width: '100%',
		},
	})

export default NavigationOnBoarding
