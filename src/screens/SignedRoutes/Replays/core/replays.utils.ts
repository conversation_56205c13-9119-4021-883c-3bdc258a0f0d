import { Image } from 'react-native'
import { DEFAULT_COLUMNS, LANDSCAPE_COLUMNS } from './replays.constants'

/**
 * Calcula o aspect ratio de uma imagem
 * @param image - URL ou nome da imagem
 * @returns Aspect ratio (largura/altura)
 */
export const calculateAspectRatio = async (image: string): Promise<number> => {
	const url = image.indexOf('http') !== -1 ? image : `https://api.oolhonolance.com.br/img/arenas/${image}.png`

	return new Promise((resolve, reject) => {
		Image.getSize(
			url,
			(width, height) => {
				resolve(width / height)
			},
			(error) => {
				reject(error)
			}
		)
	})
}

/**
 * Determina o número de colunas baseado no aspect ratio
 * @param aspectRatio - Aspect ratio da imagem
 * @returns Número de colunas (2 ou 3)
 */
export const getColumnsFromAspectRatio = (aspectRatio: number): number =>
	aspectRatio > 1 ? DEFAULT_COLUMNS : LANDSCAPE_COLUMNS

/**
 * Calcula a largura do item baseado na tela e número de colunas
 * @param screenWidth - Largura da tela
 * @param containerPadding - Padding do container
 * @param gapWidth - Largura do gap entre itens
 * @param columnCount - Número de colunas
 * @returns Largura do item
 */
export const calculateItemWidth = (
	screenWidth: number,
	containerPadding: number,
	gapWidth: number,
	columnCount: number
): number => (screenWidth - containerPadding - gapWidth * (columnCount - 1)) / columnCount
