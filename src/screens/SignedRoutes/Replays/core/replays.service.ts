import { useApi } from '../../../../stores'
import type { ReplayDay, ReplayField, ReplayHour, ReplayVideo } from '../../../../types'

const { callApi } = useApi.getState()

/**
 * Busca campos de uma arena
 * @param selectedArena - ID da arena selecionada
 */
export const fetchArenaFields = async (selectedArena: string): Promise<ReplayField[] | null> =>
	callApi({
		url: `/replay/court/${selectedArena}`,
		section: 'replayFields',
	})

/**
 * Busca dias disponíveis para um campo
 * @param selectedField - ID do campo selecionado
 */
export const fetchFieldDays = async (selectedField: string): Promise<ReplayDay[] | null> =>
	callApi({
		url: `/replay/day/${selectedField}`,
		section: 'replayDays',
	})

/**
 * Busca horários disponíveis para um dia
 * @param selectedDay - ID do dia selecionado
 * @param selectedField - ID do campo selecionado
 */
export const fetchDayHours = async (selectedDay: string, selectedField: string): Promise<ReplayHour[] | null> =>
	callApi({
		url: `/replay/hour/${selectedDay}/${selectedField}`,
		section: 'replayHours',
	})

/**
 * Busca vídeos para um horário específico
 * @param selectedHour - ID do horário selecionado
 * @param selectedField - ID do campo selecionado
 */
export const fetchHourVideos = async (selectedHour: string, selectedField: string): Promise<ReplayVideo[] | null> =>
	callApi({
		url: `/replay/videos/${selectedHour}/${selectedField}`,
		section: 'replayVideos',
	})
