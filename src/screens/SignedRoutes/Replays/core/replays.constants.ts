import { TUTORIAL_STORAGE_KEYS } from '../../../../constants/storage'
import type { StorageKey } from '../../../../types'

/**
 * Constantes para a tela Replays
 */

// Storage keys - importadas das constantes centralizadas
export const TUTORIAL_STORAGE_KEY: StorageKey = TUTORIAL_STORAGE_KEYS.REPLAYS || '@olhonolance:tutorial_replays'

// Validação em runtime para garantir que a constante está definida
if (!TUTORIAL_STORAGE_KEY) {
	console.error('[ERROR] TUTORIAL_STORAGE_KEY is undefined. Check storage constants import.')
}

const NAVBAR_HEIGHT = 45
const FILTER_BAR_EXPANDED_HEIGHT = 200
const FILTER_BAR_ANIMATION_DURATION = 300

const ITEMS_PER_PAGE = 20

const VIDEO_ASPECT_RATIO = 16 / 9
const DEFAULT_ASPECT_RATIO = VIDEO_ASPECT_RATIO

const NAVIGATION_HEIGHT = 80

const FILTER_MARGIN = 10

// Video constants
const VIDEO_PLAY_DELAY = 500
const VIDEO_SWIPE_ANIMATION = 'slide_from_bottom'

// Grid columns
const DEFAULT_COLUMNS = 2
const LANDSCAPE_COLUMNS = 3

// Exports
export {
	DEFAULT_ASPECT_RATIO,
	DEFAULT_COLUMNS,
	FILTER_BAR_ANIMATION_DURATION,
	FILTER_BAR_EXPANDED_HEIGHT,
	FILTER_MARGIN,
	ITEMS_PER_PAGE,
	LANDSCAPE_COLUMNS,
	NAVBAR_HEIGHT,
	NAVIGATION_HEIGHT,
	VIDEO_ASPECT_RATIO,
	VIDEO_PLAY_DELAY,
	VIDEO_SWIPE_ANIMATION,
}
