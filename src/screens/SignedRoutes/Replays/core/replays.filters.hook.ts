import { useCallback } from 'react'
import { useApi } from '../../../../stores'
import { useFilterStore } from './filters.store'
import { fetchArenaFields, fetchDayHours, fetchFieldDays, fetchHourVideos } from './replays.service'

export const useReplayFilters = () => {
	const { sectionLoading } = useApi()
	const {
		selectedArena,
		fields,
		selectedField,
		days,
		selectedDay,
		hours,
		selectedHour,
		isLoading,
		setSelectedArena,
		setFields,
		setSelectedField,
		setDays,
		setSelectedDay,
		setHours,
		setSelectedHour,
		setLoading,
		clearFilters,
		clearSubsequentFilters,
		reset,
	} = useFilterStore()

	// Função para buscar vídeos (última etapa da cascata)
	const loadVideos = useCallback(async (hourId: string, fieldId: string) => {
		try {
			const videos = await fetchHourVideos(hourId, fieldId)
			return videos
		} catch (error) {
			console.warn('Erro ao carregar vídeos do hour:', error)
			return null
		}
	}, [])

	// Função para carregar horas e auto-selecionar a primeira
	const loadHoursAndSelectFirst = useCallback(
		async (dayId: string, fieldId: string) => {
			try {
				const hoursData = await fetchDayHours(dayId, fieldId)

				if (hoursData && Array.isArray(hoursData) && hoursData.length > 0) {
					setHours(hoursData)
					const firstHour = hoursData[0]

					if (firstHour?.id_video_hour) {
						setSelectedHour(firstHour.id_video_hour)
						const videos = await loadVideos(firstHour.id_video_hour, fieldId)
						return videos
					}
				}
			} catch (error) {
				console.warn('Erro ao carregar hours do day:', error)
			}
			return null
		},
		[setHours, setSelectedHour, loadVideos]
	)

	// Função para carregar dias e auto-selecionar o primeiro
	const loadDaysAndSelectFirst = useCallback(
		async (fieldId: string) => {
			try {
				const daysData = await fetchFieldDays(fieldId)

				if (daysData && Array.isArray(daysData) && daysData.length > 0) {
					setDays(daysData)
					const firstDay = daysData[0]

					if (firstDay?.id_video_day) {
						setSelectedDay(firstDay.id_video_day)
						const videos = await loadHoursAndSelectFirst(firstDay.id_video_day, fieldId)
						return videos
					}
				}
			} catch (error) {
				console.warn('Erro ao carregar days do field:', error)
			}
			return null
		},
		[setDays, setSelectedDay, loadHoursAndSelectFirst]
	)

	// Funções principais para atualizações manuais
	const updateSelectedHour = useCallback(
		async (hourId: string) => {
			clearSubsequentFilters('hour')
			setSelectedHour(hourId)

			if (hourId && selectedField) {
				return loadVideos(hourId, selectedField)
			}
			return null
		},
		[selectedField, clearSubsequentFilters, setSelectedHour, loadVideos]
	)

	const updateSelectedDay = useCallback(
		async (dayId: string) => {
			clearSubsequentFilters('day')
			setSelectedDay(dayId)

			if (dayId && selectedField) {
				return loadHoursAndSelectFirst(dayId, selectedField)
			}
			return null
		},
		[selectedField, clearSubsequentFilters, setSelectedDay, loadHoursAndSelectFirst]
	)

	const updateSelectedField = useCallback(
		async (fieldId: string) => {
			clearSubsequentFilters('field')
			setSelectedField(fieldId)

			if (fieldId) {
				return loadDaysAndSelectFirst(fieldId)
			}
			return null
		},
		[clearSubsequentFilters, setSelectedField, loadDaysAndSelectFirst]
	)

	const updateSelectedArena = useCallback(
		async (arenaId: string) => {
			clearFilters()
			setSelectedArena(String(arenaId))

			if (arenaId) {
				try {
					const fieldsData = await fetchArenaFields(String(arenaId))
					if (fieldsData && Array.isArray(fieldsData) && fieldsData.length > 0) {
						setFields(fieldsData)
						const firstField = fieldsData[0]
						if (firstField?.id_court) {
							setSelectedField(firstField.id_court)
							return loadDaysAndSelectFirst(firstField.id_court)
						}
					}
				} catch (error) {
					console.warn('Erro ao carregar fields da arena:', error)
				}
			}
			return null
		},
		[clearFilters, setSelectedArena, setFields, setSelectedField, loadDaysAndSelectFirst]
	)

	const isLoadingFields = sectionLoading.replayFields || false
	const isLoadingDays = sectionLoading.replayDays || false
	const isLoadingHours = sectionLoading.replayHours || false
	const isLoadingVideos = sectionLoading.replayVideos || false

	return {
		selectedArena,
		fields,
		selectedField,
		days,
		selectedDay,
		hours,
		selectedHour,
		isLoading,
		isLoadingFields,
		isLoadingDays,
		isLoadingHours,
		isLoadingVideos,
		updateSelectedArena,
		updateSelectedField,
		updateSelectedDay,
		updateSelectedHour,
		clearFilters,
		clearSubsequentFilters,
		reset,
	}
}
