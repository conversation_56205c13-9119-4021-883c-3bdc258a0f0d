import { useCallback, useEffect, useRef, useState } from 'react'
import { useFavoriteArenas } from '../../../../features'
import { useReplayStore } from './replays.store'

export const useReplays = () => {
	const [isReady, setIsReady] = useState(false)
	const [isSharing, setIsSharing] = useState(false)
	const [isDownloading, setIsDownloading] = useState(false)

	const { initializeFavoriteArenas } = useFavoriteArenas()
	const videoRef = useRef<any>(null)
	const store = useReplayStore()

	useEffect(() => {
		store.setVideoRef(videoRef.current)
	}, [])

	useEffect(() => {
		const initialize = async () => {
			await initializeFavoriteArenas()
			setIsReady(true)
		}
		initialize()
	}, [initializeFavoriteArenas])

	const pauseVideo = useCallback(async (): Promise<boolean> => {
		if (videoRef.current) {
			try {
				await videoRef.current.pauseAsync()
				return true
			} catch {
				return false
			}
		}
		return false
	}, [])

	const playVideo = useCallback(async (delay = 300): Promise<boolean> => {
		if (videoRef.current) {
			try {
				setTimeout(async () => {
					try {
						await videoRef.current.playAsync()
					} catch {}
				}, delay)
				return true
			} catch {
				return false
			}
		}
		return false
	}, [])

	const handleSelectVideo = useCallback(
		async (video: any) => {
			await pauseVideo()
			store.setSelectedVideo(video)
			store.setFiltersVisible(false)
			playVideo(300)
		},
		[pauseVideo, playVideo]
	)

	const handleCameraChange = useCallback(() => {
		const { selectedVideo } = store
		if (!selectedVideo || !selectedVideo.camera2) return

		const { camera2, ...rest } = selectedVideo
		const newVideo = { camera2: { ...rest }, ...camera2 }
		store.setSelectedVideo(newVideo)
	}, [])

	const prepareVideoExpansion = useCallback(
		() => ({
			selectedVideo: store.selectedVideo,
			videos: store.videos,
			arenaId: '',
		}),
		[]
	)

	const handleExpandVideo = useCallback(async () => {
		await pauseVideo()
		return prepareVideoExpansion()
	}, [pauseVideo, prepareVideoExpansion])

	const syncFilterState = useCallback(() => {
		const { selectedVideo, filtersVisible } = store
		if (selectedVideo && filtersVisible) {
			store.setFiltersVisible(false)
		} else if (!selectedVideo && !filtersVisible) {
			store.setFiltersVisible(true)
		}
	}, [])

	const toggleFilters = useCallback((visible: boolean) => {
		store.setFiltersVisible(visible)
	}, [])

	const shareVideo = useCallback(async (video: any) => {
		setIsSharing(true)
		try {
			// TODO: Implementar compartilhamento de vídeo
		} catch (error) {
			console.warn('Erro ao compartilhar vídeo:', error)
		} finally {
			setIsSharing(false)
		}
	}, [])

	const downloadVideo = useCallback(async (video: any) => {
		setIsDownloading(true)
		try {
			// TODO: Implementar download de vídeo
		} catch (error) {
			console.warn('Erro ao baixar vídeo:', error)
		} finally {
			setIsDownloading(false)
		}
	}, [])

	const handleVideosAvailable = useCallback((newVideos: any[]) => {
		store.setVideos(newVideos)
	}, [])

	return {
		isReady,
		isSharing,
		isDownloading,
		videoRef,
		selectedVideo: store.selectedVideo,
		videos: store.videos,
		filtersVisible: store.filtersVisible,
		shouldRemeasureFilterBar: store.shouldRemeasureFilterBar,
		pauseVideo,
		playVideo,
		handleSelectVideo,
		handleCameraChange,
		prepareVideoExpansion,
		handleExpandVideo,
		syncFilterState,
		toggleFilters,
		shareVideo,
		downloadVideo,
		handleVideosAvailable,
		setVideos: store.setVideos,
		clearVideos: store.clearVideos,
		setFiltersVisible: store.setFiltersVisible,
		setShouldRemeasureFilterBar: store.setShouldRemeasureFilterBar,
		resetSelectedVideo: store.resetSelectedVideo,
		resetReplayData: store.resetReplayData,
	}
}
