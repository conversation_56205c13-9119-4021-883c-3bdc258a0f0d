import { create } from 'zustand'

interface FilterStoreState {
	// Estados específicos dos filtros de arena
	selectedArena: string
	fields: any[]
	selectedField: string
	days: any[]
	selectedDay: string
	hours: any[]
	selectedHour: string
	isLoading: boolean
}

interface FilterStoreActions {
	// Setters para arena selecionada
	setSelectedArena: (arenaId: string) => void

	// Setters para filtros
	setFields: (fields: any[]) => void
	setSelectedField: (fieldId: string) => void
	setDays: (days: any[]) => void
	setSelectedDay: (dayId: string) => void
	setHours: (hours: any[]) => void
	setSelectedHour: (hourId: string) => void

	// Controle de loading
	setLoading: (loading: boolean) => void

	// Limpeza
	clearFilters: () => void
	clearSubsequentFilters: (level: 'arena' | 'field' | 'day' | 'hour') => void
	reset: () => void
}

type FilterStore = FilterStoreState & FilterStoreActions

const initialState: FilterStoreState = {
	selectedArena: '',
	fields: [],
	selectedField: '',
	days: [],
	selectedDay: '',
	hours: [],
	selectedHour: '',
	isLoading: false,
}

export const useFilterStore = create<FilterStore>((set, get) => ({
	...initialState,

	// ============================================
	// SETTERS BÁSICOS
	// ============================================

	setSelectedArena: (arenaId: string) => {
		set({ selectedArena: arenaId })
	},

	setFields: (fields: any[]) => {
		set({ fields })
	},

	setSelectedField: (fieldId: string) => {
		set({ selectedField: fieldId })
	},

	setDays: (days: any[]) => {
		set({ days })
	},

	setSelectedDay: (dayId: string) => {
		set({ selectedDay: dayId })
	},

	setHours: (hours: any[]) => {
		set({ hours })
	},

	setSelectedHour: (hourId: string) => {
		set({ selectedHour: hourId })
	},

	setLoading: (loading: boolean) => {
		set({ isLoading: loading })
	},

	// ============================================
	// LIMPEZA DE FILTROS
	// ============================================

	clearFilters: () => {
		set({
			selectedField: '',
			selectedDay: '',
			selectedHour: '',
			fields: [],
			days: [],
			hours: [],
		})
	},

	clearSubsequentFilters: (level: 'arena' | 'field' | 'day' | 'hour') => {
		if (level === 'arena') {
			set({
				selectedField: '',
				selectedDay: '',
				selectedHour: '',
				fields: [],
				days: [],
				hours: [],
			})
		} else if (level === 'field') {
			set({
				selectedDay: '',
				selectedHour: '',
				days: [],
				hours: [],
			})
		} else if (level === 'day') {
			set({
				selectedHour: '',
				hours: [],
			})
		}
		// level === 'hour' não limpa nada subsequente
	},

	reset: () => {
		set(initialState)
	},
}))
