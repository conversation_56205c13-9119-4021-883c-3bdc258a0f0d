import { useCallback, useEffect, useRef, useState } from 'react'
import { useReplayStore } from './replays.store'

export const useReplayVideo = () => {
	const [isSharing, setIsSharing] = useState(false)
	const [isDownloading, setIsDownloading] = useState(false)

	const videoRef = useRef<any>(null)
	const store = useReplayStore()

	// Sincronizar videoRef com o store
	useEffect(() => {
		store.setVideoRef(videoRef.current)
	}, [store])

	// ============================================
	// LÓGICA DE NEGÓCIO - CONTROLE DE VÍDEO
	// ============================================

	const pauseVideo = useCallback(async (): Promise<boolean> => {
		if (videoRef.current) {
			try {
				await videoRef.current.pauseAsync()
				return true
			} catch {
				return false
			}
		}
		return false
	}, [])

	const playVideo = useCallback(async (delay = 300): Promise<boolean> => {
		if (videoRef.current) {
			try {
				setTimeout(async () => {
					try {
						await videoRef.current.playAsync()
					} catch {
						// Ignore errors
					}
				}, delay)
				return true
			} catch {
				return false
			}
		}
		return false
	}, [])

	const handleSelectVideo = useCallback(
		async (video: any) => {
			await pauseVideo()
			store.setSelectedVideo(video)
			store.setFiltersVisible(false)
			playVideo(300)
		},
		[pauseVideo, playVideo, store]
	)

	const handleCameraChange = useCallback(() => {
		const { selectedVideo } = store
		if (!selectedVideo || !selectedVideo.camera2) return

		const { camera2, ...rest } = selectedVideo
		const newVideo = { camera2: { ...rest }, ...camera2 }
		store.setSelectedVideo(newVideo)
	}, [store])

	// ============================================
	// LÓGICA DE NEGÓCIO - NAVEGAÇÃO E EXPANSÃO
	// ============================================

	const prepareVideoExpansion = useCallback(
		() => ({
			selectedVideo: store.selectedVideo,
			videos: store.videos,
			arenaId: '', // Será obtido do contexto apropriado
		}),
		[store]
	)

	const handleExpandVideo = useCallback(async () => {
		await pauseVideo()
		return prepareVideoExpansion()
	}, [pauseVideo, prepareVideoExpansion])

	// ============================================
	// LÓGICA DE NEGÓCIO - INICIALIZAÇÃO
	// ============================================

	const initializeData = useCallback(
		(forceReload = false) => {
			if (store.isReady && !forceReload) return

			if (forceReload) {
				store.resetReplayData()
			}

			store.setIsReady(true)
		},
		[store]
	)

	// ============================================
	// LÓGICA DE NEGÓCIO - SYNC DE ESTADOS
	// ============================================

	const syncFilterState = useCallback(() => {
		const { selectedVideo, filtersVisible } = store
		if (selectedVideo && filtersVisible) {
			store.setFiltersVisible(false)
		} else if (!selectedVideo && !filtersVisible) {
			store.setFiltersVisible(true)
		}
	}, [store])

	const toggleFilters = useCallback(
		(visible: boolean) => {
			store.setFiltersVisible(visible)
		},
		[store]
	)

	// ============================================
	// AÇÕES DE COMPARTILHAMENTO E DOWNLOAD
	// ============================================

	const shareVideo = useCallback(async (video: any) => {
		setIsSharing(true)
		try {
			// TODO: Implementar compartilhamento de vídeo
		} catch (error) {
			console.warn('Erro ao compartilhar vídeo:', error)
		} finally {
			setIsSharing(false)
		}
	}, [])

	const downloadVideo = useCallback(async (video: any) => {
		setIsDownloading(true)
		try {
			// TODO: Implementar download de vídeo
		} catch (error) {
			console.warn('Erro ao baixar vídeo:', error)
		} finally {
			setIsDownloading(false)
		}
	}, [])

	return {
		// Estados locais do hook
		isSharing,
		setIsSharing,
		isDownloading,
		setIsDownloading,
		videoRef,

		// Estados do store
		selectedVideo: store.selectedVideo,
		videos: store.videos,
		filtersVisible: store.filtersVisible,
		isReady: store.isReady,
		shouldRemeasureFilterBar: store.shouldRemeasureFilterBar,

		// Lógica de negócio - controle de vídeo
		pauseVideo,
		playVideo,
		handleSelectVideo,
		handleCameraChange,

		// Lógica de negócio - navegação
		prepareVideoExpansion,
		handleExpandVideo,

		// Lógica de negócio - inicialização e sync
		initializeData,
		syncFilterState,
		toggleFilters,

		// Ações de compartilhamento
		shareVideo,
		downloadVideo,

		// Setters simples do store
		setVideos: store.setVideos,
		clearVideos: store.clearVideos,
		setFiltersVisible: store.setFiltersVisible,
		setShouldRemeasureFilterBar: store.setShouldRemeasureFilterBar,
		resetSelectedVideo: store.resetSelectedVideo,
		resetReplayData: store.resetReplayData,
	}
}
