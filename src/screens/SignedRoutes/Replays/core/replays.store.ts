import { create } from 'zustand'
import type { ReplayDay, ReplayField, ReplayHour, ReplayVideo } from '../../../../types'

interface ReplayStoreState {
	// APENAS estados relacionados aos replays - SEM favoriteArenas, SEM selectedArena
	selectedVideo: ReplayVideo | null
	videos: ReplayVideo[]
	filtersVisible: boolean
	isReady: boolean
	videoRef: any

	// Estados dos filtros - apenas os específicos de replays (serão movidos para FilterStore)
	fields: ReplayField[]
	selectedField: string
	days: ReplayDay[]
	selectedDay: string
	hours: ReplayHour[]
	selectedHour: string

	// Estados de UI específicos de replays
	shouldRemeasureFilterBar: boolean
}

interface ReplayStoreActions {
	// Setters simples - apenas gerenciamento de estado
	setSelectedVideo: (video: ReplayVideo | null) => void
	setVideoRef: (ref: any) => void
	setVideos: (videos: ReplayVideo[]) => void
	setFields: (fields: <PERSON><PERSON><PERSON>ield[]) => void
	setDays: (days: ReplayDay[]) => void
	setHours: (hours: ReplayHour[]) => void
	setSelectedField: (fieldId: string) => void
	setSelectedDay: (dayId: string) => void
	setSelectedHour: (hourId: string) => void
	setFiltersVisible: (visible: boolean) => void
	setShouldRemeasureFilterBar: (shouldRemeasure: boolean) => void
	setIsReady: (ready: boolean) => void

	// Reset/Clear simples
	clearVideos: () => void
	clearFilters: () => void
	resetSelectedVideo: () => void
	resetReplayData: () => void
}

type ReplayStore = ReplayStoreState & ReplayStoreActions

const initialState: ReplayStoreState = {
	selectedVideo: null,
	videos: [],
	filtersVisible: true,
	isReady: false,
	videoRef: null,
	fields: [],
	selectedField: '',
	days: [],
	selectedDay: '',
	hours: [],
	selectedHour: '',
	shouldRemeasureFilterBar: false,
}

export const useReplayStore = create<ReplayStore>((set, get) => ({
	...initialState,

	// ============================================
	// SETTERS SIMPLES - APENAS GERENCIAMENTO DE ESTADO
	// ============================================

	setSelectedVideo: (video) => {
		set({ selectedVideo: video })
	},

	setVideoRef: (ref: any) => {
		set({ videoRef: ref })
	},

	setVideos: (videos: ReplayVideo[]) => {
		set({ videos })
	},

	setFields: (fields: ReplayField[]) => {
		set({ fields })
	},

	setDays: (days: ReplayDay[]) => {
		set({ days })
	},

	setHours: (hours: ReplayHour[]) => {
		set({ hours })
	},

	setSelectedField: (fieldId: string) => {
		set({ selectedField: fieldId })
	},

	setSelectedDay: (dayId: string) => {
		set({ selectedDay: dayId })
	},

	setSelectedHour: (hourId: string) => {
		set({ selectedHour: hourId })
	},

	setFiltersVisible: (visible: boolean) => {
		set({ filtersVisible: visible })
	},

	setShouldRemeasureFilterBar: (shouldRemeasure: boolean) => {
		set({ shouldRemeasureFilterBar: shouldRemeasure })
	},

	setIsReady: (ready: boolean) => {
		set({ isReady: ready })
	},

	// ============================================
	// RESET/CLEAR SIMPLES
	// ============================================

	clearVideos: () => {
		set({ videos: [], selectedVideo: null })
	},

	clearFilters: () => {
		set({
			selectedField: '',
			selectedDay: '',
			selectedHour: '',
			fields: [],
			days: [],
			hours: [],
			videos: [],
			selectedVideo: null,
		})
	},

	resetSelectedVideo: () => {
		set({ selectedVideo: null })
	},

	resetReplayData: () => {
		set({ ...initialState })
	},
}))
