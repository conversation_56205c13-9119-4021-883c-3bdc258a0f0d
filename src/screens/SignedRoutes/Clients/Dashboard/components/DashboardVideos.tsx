import React from 'react'
import { FlatList, View } from 'react-native'
import Image from 'react-native-fast-image'

import { Text } from '../../../../../components'
import type { DashboardVideo } from '../../../../../types'

import { DASHBOARD_TEXTS } from '../core/dashboard.constants'
import { useDashboard } from '../core/dashboard.hook'
import { createDashboardStyles } from '../core/dashboard.styles'

interface RenderItemProps {
	item: DashboardVideo
}

const DashboardVideos: React.FC = () => {
	const { dashboard } = useDashboard()
	const styles = createDashboardStyles()

	const renderLastVideos = ({ item }: RenderItemProps) => (
		<View style={styles.videoContainer}>
			<Text size={'small'} style={styles.videoTitle} numberOfLines={1}>
				{item.arena}
			</Text>

			<Image
				source={{
					uri: `${item.link_amazon_thumb}?hash=${Math.random()}`,
					priority: Image.priority.high,
				}}
				style={styles.videoImage as any}
				resizeMode={'contain'}
			/>

			<View style={styles.videoContain}>
				<Text size={'small'} style={styles.videoContainText}>
					{item.court}
				</Text>
				<Text size={'small'} style={styles.videoContainText}>
					{item.time_video}
				</Text>
			</View>
		</View>
	)

	return (
		<View style={styles.body}>
			<Text size={'large'} style={styles.title}>
				{DASHBOARD_TEXTS.LAST_VIDEOS_TITLE}
			</Text>

			<FlatList
				contentContainerStyle={styles.containerList}
				columnWrapperStyle={{ justifyContent: 'space-between' }}
				data={dashboard.last_videos}
				keyExtractor={(_, index) => index.toString()}
				renderItem={renderLastVideos}
				numColumns={2}
				style={{ height: 500 }}
			/>
		</View>
	)
}

export default React.memo(DashboardVideos)
