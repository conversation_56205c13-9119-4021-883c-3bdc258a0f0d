import React from 'react'
import { ScrollView, View } from 'react-native'

import { Box } from '../../../../../components'

import { DASHBOARD_TEXTS } from '../core/dashboard.constants'
import { useDashboard } from '../core/dashboard.hook'
import { createDashboardStyles } from '../core/dashboard.styles'

const DashboardStats: React.FC = () => {
	const { dashboard } = useDashboard()
	const styles = createDashboardStyles()

	return (
		<View>
			<ScrollView horizontal contentContainerStyle={styles.containerIndicators}>
				<Box label={DASHBOARD_TEXTS.TOTAL_ARENAS} value={dashboard.arenas} />
				<Box label={DASHBOARD_TEXTS.TOTAL_DOWNLOADS} value={dashboard.log_download} />
				<Box label={DASHBOARD_TEXTS.TOTAL_COURTS} value={dashboard.courts_active} />
				<Box label={DASHBOARD_TEXTS.TOTAL_VIDEOS} value={dashboard.videos} />
			</ScrollView>
		</View>
	)
}

export default React.memo(DashboardStats)
