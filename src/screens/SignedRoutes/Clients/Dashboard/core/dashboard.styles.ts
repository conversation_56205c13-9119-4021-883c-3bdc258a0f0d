import { StyleSheet } from 'react-native'
import { useTheme } from '../../../../../stores'

export const createDashboardStyles = () => {
	const { theme } = useTheme()

	return StyleSheet.create({
		container: {
			flex: 1,
		},
		containerIndicators: {
			gap: theme.spacing.s2,
			padding: theme.spacing.s1,
		},
		body: {
			flex: 1,
			margin: theme.spacing.s1,
			gap: theme.spacing.s2,
			paddingVertical: theme.spacing.s2,
			backgroundColor: theme.colors.secondary,
			borderRadius: theme.spacing.s1,
		},
		title: {
			fontWeight: '800',
			color: theme.colors.primary,
			textAlign: 'center',
		},
		modal: {
			height: '50%',
			alignItems: 'center',
			justifyContent: 'center',
		},
		containerList: {
			paddingHorizontal: theme.spacing.s1,
			gap: theme.spacing.s1,
		},
		videoContainer: {
			borderRadius: theme.spacing.s1,
			backgroundColor: theme.colors.background,
			padding: theme.spacing.s1,
			width: '49%',
			gap: theme.spacing.s1,
		},
		videoTitle: {
			flex: 1,
			fontWeight: 'semibold',
			color: theme.colors.primary,
			paddingHorizontal: 4,
			textAlign: 'center',
		},
		videoImage: {
			width: '100%',
			aspectRatio: 16 / 9,
			borderRadius: theme.spacing.s1,
		},
		videoContain: {
			flexDirection: 'row',
			justifyContent: 'space-between',
		},
		videoContainText: {
			fontWeight: 'semibold',
			fontSize: theme.fontSize.medium,
		},
	})
}
