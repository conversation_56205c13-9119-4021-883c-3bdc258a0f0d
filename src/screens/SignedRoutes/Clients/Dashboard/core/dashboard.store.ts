import { create } from 'zustand'
import type { DashboardData } from '../../../../../types'

interface DashboardState {
	dashboard: DashboardData
}

interface DashboardActions {
	setDashboard: (dashboardData: DashboardData) => void
	resetDashboard: () => void
}

type DashboardStore = DashboardState & DashboardActions

const initialState: DashboardState = {
	dashboard: {
		arenas: 0,
		courts_active: 0,
		log_download: 0,
		videos: 0,
		last_videos: [],
	},
}

export const useDashboardStore = create<DashboardStore>((set) => ({
	...initialState,

	setDashboard: (dashboardData: DashboardData) =>
		set({
			dashboard: dashboardData,
		}),

	resetDashboard: () => set(initialState),
}))
