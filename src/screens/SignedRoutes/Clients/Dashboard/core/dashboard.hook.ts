import { useEffect } from 'react'

import { ErrorMessage } from '../../../../../helpers/HandleMessages'
import { useApi } from '../../../../../stores'

import { DASHBOARD_API_CONFIG, DASHBOARD_ERROR_MESSAGES } from './dashboard.constants'
import { fetchDashboardData } from './dashboard.service'
import { useDashboardStore } from './dashboard.store'

export const useDashboard = () => {
	const { sectionLoading } = useApi()
	const { dashboard, setDashboard } = useDashboardStore()

	const loading = sectionLoading[DASHBOARD_API_CONFIG.SECTION] || false

	useEffect(() => {
		const loadDashboardData = async (): Promise<void> => {
			try {
				const data = await fetchDashboardData()
				if (data) {
					setDashboard(data)
				}
			} catch (error: any) {
				ErrorMessage({
					title: 'Erro no Dashboard',
					message: error.response?.data?.message || DASHBOARD_ERROR_MESSAGES.FETCH_ERROR,
				})
			}
		}

		loadDashboardData()
	}, [setDashboard])

	return {
		dashboard,
		loading,
	}
}
