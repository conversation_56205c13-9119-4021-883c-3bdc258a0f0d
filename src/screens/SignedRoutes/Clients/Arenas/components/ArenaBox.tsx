import { StyleSheet, TouchableOpacity, View } from 'react-native'
import { Icon, Text } from '../../../../../components'
import { useTheme } from '../../../../../stores'

interface ArenaBoxProps {
	field: string
	time: number
	transmissionKey: boolean
	live: boolean
	status: boolean
	onPress: () => void
}

const ArenaBox = ({ field, time, transmissionKey, live, status, onPress }: ArenaBoxProps) => {
	const { theme } = useTheme()
	const styles = ArenaBox.styles(theme)

	const TextIcon = ({ iconName, text, iconStyle = {} }: { iconName: string; text: string; iconStyle?: object }) => (
		<View style={styles.groupIcon}>
			<Icon
				name={iconName}
				library={'MaterialCommunityIcons'}
				size={'extraLarge'}
				color={theme.colors.white}
				style={iconStyle}
			/>
			<Text size={'small'}>{text}</Text>
		</View>
	)

	return (
		<TouchableOpacity onPress={onPress} style={styles.box}>
			<Text style={styles.boxTitle}>{field}</Text>
			<View style={styles.rowsWrapper}>
				<View style={styles.boxRow}>
					<TextIcon iconName={'timer-outline'} text={`${time}s`} />
					<TextIcon iconName={'key'} text={transmissionKey ? 'Ativo' : 'Desativado'} />
				</View>
				<View style={styles.boxRow}>
					<TextIcon iconName={'television-play'} text={live ? 'Ativo' : 'Desativado'} />
					<TextIcon iconName={'check-underline'} text={status ? 'Ativo' : 'Desativado'} />
				</View>
			</View>
		</TouchableOpacity>
	)
}

ArenaBox.styles = (theme: any) =>
	StyleSheet.create({
		box: {
			backgroundColor: theme.colors.secondary,
			borderRadius: theme.spacing.s1,
			gap: theme.spacing.s2,
			padding: theme.spacing.s2,
			minWidth: 150,
			alignItems: 'center',
		},
		boxTitle: {
			color: theme.colors.primary,
			fontWeight: 'semibold',
			textDecorationLine: 'underline',
		},
		rowsWrapper: {
			flexDirection: 'row',
			gap: theme.spacing.s1,
		},
		boxRow: {
			flex: 1,
		},
		groupIcon: {
			alignItems: 'center',
			flex: 1,
		},
	})

export default ArenaBox
