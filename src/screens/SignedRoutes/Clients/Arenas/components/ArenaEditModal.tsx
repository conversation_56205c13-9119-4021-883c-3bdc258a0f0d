import { useMemo, useRef } from 'react'
import { StyleSheet, View } from 'react-native'
import { Button, Text } from '../../../../../components'
import { TextInput } from '../../../../../components/molecules'
import { Modal } from '../../../../../components/organisms'
import { useTheme } from '../../../../../stores'

const times = [20, 25, 30]

interface ArenaEditModalProps {
	visible: boolean
	onDismiss: () => void
	selectedArena: any
	textField: string
	time: number
	onChangeTextField: (text: string) => void
	onTimeChange: (time: number) => void
	onUpdateData: () => void
}

const ArenaEditModal = ({
	visible,
	onDismiss,
	selectedArena,
	textField,
	time,
	onChangeTextField,
	onTimeChange,
	onUpdateData,
}: ArenaEditModalProps) => {
	const { theme } = useTheme()
	const styles = useMemo(() => createStyle(theme), [theme])
	const inputRef = useRef(null)

	const renderModalBodyContent = (label: string, status: boolean) => (
		<View style={styles.modalBodyContain}>
			<Text style={styles.modalBodyFont}>{label}</Text>
			<Text style={status ? styles.fontActive : {}}>{status ? 'Ativo' : 'Desativado'}</Text>
		</View>
	)

	return (
		<Modal visible={visible} onDismiss={onDismiss} style={styles.modal}>
			<TextInput
				ref={inputRef}
				value={textField}
				onChangeText={onChangeTextField}
				style={styles.textInput}
				autoFocus={true}
			/>
			<View style={styles.modalBody}>
				<View style={styles.modalBodyContain}>
					<Text style={styles.modalBodyFont}>Tempo de vídeo: </Text>
					<View style={styles.timeButtonContainer}>
						{times.map((timeValue) => (
							<Button
								key={timeValue}
								label={timeValue.toString()}
								onPress={() => onTimeChange(timeValue)}
								type={'text'}
								style={{
									paddingVertical: 0,
									paddingHorizontal: theme.spacing.s2,
									borderRadius: 0,
									backgroundColor: time === timeValue ? theme.colors.primary : 'transparent',
								}}
							/>
						))}
					</View>
				</View>
				{renderModalBodyContent('Chave de transmissão:', selectedArena.court_live)}
				{renderModalBodyContent('Live:', selectedArena.live_on)}
				{renderModalBodyContent('Status:', selectedArena.active)}
				<View style={{ marginTop: theme.spacing.s3 }}>
					<Button onPress={onUpdateData} label={'Atualizar'} />
				</View>
			</View>
		</Modal>
	)
}

const createStyle = (theme: any) =>
	StyleSheet.create({
		modal: {
			gap: theme.spacing.s4,
			paddingVertical: theme.spacing.s10,
			paddingHorizontal: theme.spacing.s2,
		},
		modalBody: {
			gap: theme.spacing.s1,
		},
		modalBodyContain: {
			alignItems: 'center',
			flexDirection: 'row',
			justifyContent: 'space-between',
		},
		modalBodyFont: {
			fontWeight: 'bold',
		},
		fontActive: {
			color: theme.colors.primary,
		},
		textInput: {
			color: theme.colors.primary,
			fontWeight: 'semibold',
			textAlign: 'center',
			fontSize: theme.fontSize.extraLarge,
			textDecorationLine: 'underline',
		},
		timeButtonContainer: {
			flexDirection: 'row',
			borderWidth: 1,
			borderRadius: theme.spacing.s1,
		},
	})

export default ArenaEditModal
