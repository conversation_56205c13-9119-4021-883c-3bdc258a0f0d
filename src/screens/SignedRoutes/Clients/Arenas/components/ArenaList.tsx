import { FlatList, StyleSheet, View } from 'react-native'
import { ScrollView } from 'react-native-gesture-handler'
import { Text } from '../../../../../components'
import { useTheme } from '../../../../../stores'
import ArenaBox from './ArenaBox'
import ArenaEditModal from './ArenaEditModal'

interface Arena {
	id_arena: number
	arena: string
	courts: Court[]
}

interface Court {
	id_court: number
	court: string
	time_video: number
	court_live: boolean
	live_on: boolean
	active: boolean
}

interface ArenaListProps {
	arenas: Arena[]
	modalVisible: boolean
	selectedArena: Court | null
	textField: string
	time: number
	openEditModal: (court: Court) => void
	closeEditModal: () => void
	setTextField: (text: string) => void
	setTime: (time: number) => void
	updateData: () => void
}

const ArenaList = ({
	arenas,
	modalVisible,
	selectedArena,
	textField,
	time,
	openEditModal,
	closeEditModal,
	setTextField,
	setTime,
	updateData,
}: ArenaListProps) => {
	const { theme } = useTheme()
	const styles = ArenaList.styles(theme)

	return (
		<ScrollView contentContainerStyle={styles.container}>
			{arenas.map((arena) => (
				<View key={arena.id_arena}>
					<Text size={'large'} style={styles.title}>
						{arena.arena}
					</Text>
					<FlatList
						horizontal
						data={arena?.courts}
						contentContainerStyle={styles.flatListContent}
						keyExtractor={(field) => field.id_court.toString()}
						renderItem={({ item }) => (
							<ArenaBox
								field={item.court}
								time={item.time_video}
								transmissionKey={item.court_live}
								live={item.live_on}
								status={item.active}
								onPress={() => openEditModal(item)}
							/>
						)}
					/>
				</View>
			))}

			<ArenaEditModal
				visible={modalVisible}
				onDismiss={closeEditModal}
				selectedArena={selectedArena}
				textField={textField}
				time={time}
				onChangeTextField={setTextField}
				onTimeChange={setTime}
				onUpdateData={updateData}
			/>
		</ScrollView>
	)
}

ArenaList.styles = (theme: any) =>
	StyleSheet.create({
		container: {
			gap: theme.spacing.s2,
		},
		title: {
			textAlign: 'center',
			fontWeight: 'bold',
			color: theme.colors.primary,
		},
		flatListContent: {
			gap: theme.spacing.s1,
			marginHorizontal: theme.spacing.s1,
			marginVertical: theme.spacing.s1,
		},
	})

export default ArenaList
