import { useCallback, useEffect, useState } from 'react'
import { ErrorMessage, SuccessMessage } from '../../../../../helpers/HandleMessages'
import { useApi } from '../../../../../stores'
import { ARENAS_API_CONFIG, ARENAS_TEXTS } from './arenas.constants'
import { fetchArenas, updateCourt } from './arenas.service'

export function useArenaData() {
	const { sectionLoading } = useApi()
	const [arenas, setArenas] = useState<any[]>([])
	const [modalVisible, setModalVisible] = useState(false)
	const [selectedArena, setSelectedArena] = useState<any>({})
	const [textField, setTextField] = useState('')
	const [time, setTime] = useState(0)

	const loading = sectionLoading[ARENAS_API_CONFIG.SECTION] || false

	const fetchArenasData = useCallback(async () => {
		try {
			const data = await fetchArenas()
			setArenas(data)
		} catch (error: any) {
			ErrorMessage({
				message: error.response?.data?.message || ARENAS_TEXTS.FETCH_ERROR,
			})
		}
	}, [])

	useEffect(() => {
		fetchArenasData()
	}, [fetchArenasData])

	const openEditModal = (arena: any) => {
		setSelectedArena(arena)
		setTextField(arena.court)
		setTime(arena.time_video)
		setModalVisible(true)
	}

	const closeEditModal = () => setModalVisible(false)

	const updateData = useCallback(async () => {
		try {
			const data = await updateCourt(selectedArena.id_court, {
				court: textField,
				time_video: time,
			})
			closeEditModal()
			SuccessMessage({
				message: data.message || ARENAS_TEXTS.UPDATE_SUCCESS,
			})
			fetchArenasData()
		} catch (error: any) {
			closeEditModal()
			ErrorMessage({
				message: error.response?.data?.message || ARENAS_TEXTS.UPDATE_ERROR,
			})
		}
	}, [selectedArena, textField, time, fetchArenasData])

	return {
		arenas,
		modalVisible,
		selectedArena,
		textField,
		time,
		loading,
		openEditModal,
		closeEditModal,
		setTextField,
		setTime,
		updateData,
	}
}
