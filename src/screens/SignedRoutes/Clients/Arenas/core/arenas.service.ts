import { useApi } from '../../../../../stores'
import { ARENAS_API_CONFIG } from './arenas.constants'

const { callApi } = useApi.getState()

export const fetchArenas = async (): Promise<any> =>
	callApi({
		method: 'GET',
		url: ARENAS_API_CONFIG.FETCH_ENDPOINT,
		section: ARENAS_API_CONFIG.SECTION,
	})

export const updateCourt = async (courtId: string, courtData: any): Promise<any> =>
	callApi({
		method: 'PUT',
		url: `${ARENAS_API_CONFIG.UPDATE_ENDPOINT}/${courtId}`,
		data: courtData,
		section: ARENAS_API_CONFIG.SECTION,
	})
