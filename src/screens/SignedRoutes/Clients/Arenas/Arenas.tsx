import { View } from 'react-native'

import ArenaList from './components/ArenaList'
import { useArenaData } from './core/arenas.hook'

const Arenas = () => {
	const {
		arenas,
		modalVisible,
		selectedArena,
		textField,
		time,
		openEditModal,
		closeEditModal,
		setTextField,
		setTime,
		updateData,
	} = useArenaData()

	return (
		<View style={{ flex: 1 }}>
			<ArenaList
				arenas={arenas}
				modalVisible={modalVisible}
				selectedArena={selectedArena}
				textField={textField}
				time={time}
				openEditModal={openEditModal}
				closeEditModal={closeEditModal}
				setTextField={setTextField}
				setTime={setTime}
				updateData={updateData}
			/>
		</View>
	)
}

export default Arenas
