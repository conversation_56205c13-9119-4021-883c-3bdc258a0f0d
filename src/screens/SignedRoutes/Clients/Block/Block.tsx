import { useFocusEffect } from '@react-navigation/native'
import { useCallback } from 'react'
import { View } from 'react-native'

import BlockFormModal from './components/BlockFormModal'
import BlockList from './components/BlockList'
import { useBlockStore } from './core/block.store'

const Block = () => {
	const { fetchBlocks } = useBlockStore()

	useFocusEffect(
		useCallback(() => {
			fetchBlocks()
		}, [fetchBlocks])
	)

	return (
		<View style={{ flex: 1 }}>
			<BlockList />
			<BlockFormModal />
		</View>
	)
}

export default Block
