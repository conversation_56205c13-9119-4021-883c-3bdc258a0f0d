import { useEffect, useMemo } from 'react'
import { ScrollView, StyleSheet, View } from 'react-native'

import { Button, Dropdown, Text } from '../../../../../components'
import { TextInput } from '../../../../../components/molecules'
import { Modal } from '../../../../../components/organisms'
import { useTheme } from '../../../../../stores'
import { useBlockStore } from '../core/block.store'

const BlockFormModal = () => {
	const { theme } = useTheme()
	const {
		modalVisible,
		courts,
		days,
		selectedCourt,
		selectedDay,
		hourInitial,
		hourFinal,
		loading,
		closeBlockModal,
		setSelectedCourt,
		setSelectedDay,
		setHourInitial,
		setHourFinal,
		createBlock,
	} = useBlockStore()

	const styles = useMemo(() => createStyles(theme), [theme])

	const handleTimeInput = (text: string, setter: (value: string) => void) => {
		const numbers = text.replace(/\D/g, '')
		const limitedNumbers = numbers.slice(0, 4)

		if (limitedNumbers.length >= 3) {
			const formatted = `${limitedNumbers.slice(0, 2)}:${limitedNumbers.slice(2)}`
			setter(formatted)
		} else {
			setter(limitedNumbers)
		}
	}

	const formatCourtsForSelect = useMemo(
		() =>
			courts.map((court: any) => ({
				label: `${court.arena} - ${court.court}`,
				value: court.id_court,
			})),
		[courts]
	)

	const formatDaysForSelect = useMemo(
		() =>
			days.map((day: any) => ({
				label: day.day,
				value: day.id_video_day,
			})),
		[days]
	)

	// Formatar os valores iniciais quando necessário
	useEffect(() => {
		if (hourInitial && hourInitial.length >= 3 && !hourInitial.includes(':')) {
			handleTimeInput(hourInitial, setHourInitial)
		}
		if (hourFinal && hourFinal.length >= 3 && !hourFinal.includes(':')) {
			handleTimeInput(hourFinal, setHourFinal)
		}
	}, [hourInitial, hourFinal])

	return (
		<Modal visible={modalVisible} onDismiss={closeBlockModal} style={styles.modal}>
			<Text style={styles.title}>Novo Bloqueio</Text>

			<ScrollView contentContainerStyle={styles.form} keyboardShouldPersistTaps={'handled'}>
				<Dropdown
					label={'Quadra'}
					placeholder={'Selecione a quadra'}
					items={formatCourtsForSelect}
					value={selectedCourt}
					onValueChange={setSelectedCourt}
				/>

				{selectedCourt && (
					<Dropdown
						label={'Dia'}
						placeholder={'Selecione o dia'}
						items={formatDaysForSelect}
						value={selectedDay}
						onValueChange={setSelectedDay}
					/>
				)}

				<View style={styles.timeInputsContainer}>
					<View style={styles.timeInput}>
						<TextInput
							label={'Hora Inicial'}
							value={hourInitial}
							onChangeText={(text) => handleTimeInput(text, setHourInitial)}
							placeholder={'08:00'}
							keyboardType={'number-pad'}
							maxLength={5}
						/>
					</View>

					<View style={styles.timeInput}>
						<TextInput
							label={'Hora Final'}
							value={hourFinal}
							onChangeText={(text) => handleTimeInput(text, setHourFinal)}
							placeholder={'09:00'}
							keyboardType={'number-pad'}
							maxLength={5}
						/>
					</View>
				</View>

				<Text style={styles.hint}>Formato: HH:MM (exemplo: 08:30)</Text>
			</ScrollView>

			<View style={styles.buttonContainer}>
				<Button label={'Bloquear'} onPress={createBlock} loading={loading} style={styles.submitButton} />
			</View>
		</Modal>
	)
}

const createStyles = (theme: any) =>
	StyleSheet.create({
		modal: {
			padding: theme.spacing.s2,
		},
		title: {
			fontSize: theme.fontSize.large,
			fontWeight: 'semibold',
			textAlign: 'center',
			marginBottom: theme.spacing.s3,
			color: theme.colors.primary,
		},
		form: {
			gap: theme.spacing.s2,
		},
		timeInputsContainer: {
			flexDirection: 'row',
			gap: theme.spacing.s2,
		},
		timeInput: {
			flex: 1,
		},
		hint: {
			fontSize: theme.fontSize.small,
			color: theme.colors.textLight,
			fontStyle: 'italic',
		},
		buttonContainer: {
			flexDirection: 'row',
			justifyContent: 'flex-end',
			gap: theme.spacing.s2,
		},
		submitButton: {
			// height: 40,
			// minWidth: 90,
			// paddingVertical: 0,
		},
	})

export default BlockFormModal
