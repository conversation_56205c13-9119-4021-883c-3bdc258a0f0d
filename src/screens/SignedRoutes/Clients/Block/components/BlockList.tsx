import { useMemo } from 'react'
import { ActivityIndicator, FlatList, StyleSheet, TouchableOpacity, View } from 'react-native'

import { Button, Icon, Text } from '../../../../../components'
import { AskMessage } from '../../../../../helpers/HandleMessages'
import { useTheme } from '../../../../../stores'
import { useBlockStore } from '../core/block.store'

const BlockList = () => {
	const { theme } = useTheme()
	const { blocks, loading, deleteBlock, openBlockModal } = useBlockStore()
	const styles = useMemo(() => createStyles(theme), [theme])

	const handleDeleteBlock = (id: string) => {
		AskMessage('Remover Bloqueio', 'Tem certeza que deseja remover este bloqueio?', () => deleteBlock(id))
	}

	const renderBlockItem = ({ item }: { item: any }) => (
		<View style={styles.blockItem}>
			<View style={styles.blockInfo}>
				<Text style={styles.blockTitle}>{item.arena}</Text>
				<Text style={styles.blockSubtitle}>{item.court}</Text>
				<Text>{item.day}</Text>
				<View style={styles.timeRow}>
					<Text>Horário: </Text>
					<Text style={styles.time}>{item.hour_initial}</Text>
					<Text> até </Text>
					<Text style={styles.time}>{item.hour_final}</Text>
				</View>
			</View>

			<TouchableOpacity style={styles.deleteButton} onPress={() => handleDeleteBlock(item.id_block)}>
				<Icon name={'trash-2'} library={'Feather'} color={theme.colors.red} size={'large'} />
			</TouchableOpacity>
		</View>
	)

	const renderEmptyComponent = () => (
		<View style={styles.emptyContainer}>
			<Icon
				name={'block'}
				library={'MaterialIcons'}
				size={'extraLarge'}
				color={theme.colors.textLight}
				style={styles.emptyIcon}
			/>
			<Text style={styles.emptyText}>Nenhum bloqueio encontrado</Text>
			<Text style={styles.emptySubtext}>Use o botão abaixo para adicionar um novo bloqueio de horário</Text>
		</View>
	)

	return (
		<View style={styles.container}>
			{loading ? (
				<View style={styles.loadingContainer}>
					<ActivityIndicator size={'large'} color={theme.colors.primary} />
					<Text style={styles.loadingText}>Carregando bloqueios...</Text>
				</View>
			) : (
				<>
					<View style={styles.contentContainer}>
						<FlatList
							data={blocks}
							renderItem={renderBlockItem}
							keyExtractor={(item) => item.id_block.toString()}
							contentContainerStyle={[styles.listContent, blocks.length === 0 && styles.emptyListContent]}
							ListEmptyComponent={renderEmptyComponent}
						/>
					</View>

					<View style={styles.buttonContainer}>
						<Button
							label={'Adicionar Bloqueio'}
							onPress={openBlockModal}
							icon={{
								name: 'plus',
								library: 'Feather',
								color: theme.colors.white,
							}}
						/>
					</View>
				</>
			)}
		</View>
	)
}

const createStyles = (theme: any) =>
	StyleSheet.create({
		container: {
			flex: 1,
			padding: theme.spacing.s2,
			display: 'flex',
			flexDirection: 'column',
		},
		contentContainer: {
			flex: 1,
		},
		buttonContainer: {
			marginTop: theme.spacing.s2,
			paddingBottom: theme.spacing.s2,
		},
		listContent: {
			flexGrow: 1,
			gap: theme.spacing.s2,
		},
		emptyListContent: {
			flexGrow: 1,
			justifyContent: 'center',
		},
		blockItem: {
			backgroundColor: theme.colors.secondary,
			borderRadius: theme.spacing.s1,
			padding: theme.spacing.s2,
			flexDirection: 'row',
			justifyContent: 'space-between',
			alignItems: 'center',
		},
		blockInfo: {
			flex: 1,
			gap: theme.spacing.s1,
		},
		blockTitle: {
			fontSize: theme.fontSize.large,
			fontWeight: 'bold',
			color: theme.colors.primary,
		},
		blockSubtitle: {
			fontSize: theme.fontSize.medium,
			fontWeight: 'semibold',
		},
		timeRow: {
			flexDirection: 'row',
			alignItems: 'center',
		},
		time: {
			fontWeight: 'bold',
		},
		deleteButton: {
			padding: theme.spacing.s2,
		},
		emptyContainer: {
			alignItems: 'center',
			justifyContent: 'center',
			flex: 1,
		},
		emptyIcon: {
			marginBottom: theme.spacing.s2,
		},
		emptyText: {
			fontSize: theme.fontSize.large,
			fontWeight: 'bold',
			color: theme.colors.textLight,
		},
		emptySubtext: {
			fontSize: theme.fontSize.medium,
			color: theme.colors.textLight,
			textAlign: 'center',
			marginTop: theme.spacing.s2,
		},
		loadingContainer: {
			flex: 1,
			justifyContent: 'center',
			alignItems: 'center',
		},
		loadingText: {
			marginTop: theme.spacing.s2,
			fontSize: theme.fontSize.medium,
			color: theme.colors.textLight,
		},
	})

export default BlockList
