import { useApi } from '../../../../../stores'
import { BLOCK_API_CONFIG } from './block.constants'

const { callApi } = useApi.getState()

export const fetchBlocks = async (): Promise<any> =>
	callApi({
		method: 'GET',
		url: BLOCK_API_CONFIG.BLOCKS_ENDPOINT,
		section: BLOCK_API_CONFIG.SECTION,
	})

export const fetchArenas = async (): Promise<any> =>
	callApi({
		method: 'GET',
		url: BLOCK_API_CONFIG.ARENAS_ENDPOINT,
		section: BLOCK_API_CONFIG.SECTION,
	})

export const fetchReplaysByDay = async (courtId: string): Promise<any> =>
	callApi({
		method: 'GET',
		url: `${BLOCK_API_CONFIG.REPLAY_DAY_ENDPOINT}/${courtId}`,
		section: BLOCK_API_CONFIG.SECTION,
	})

export const createBlock = async (blockData: any): Promise<any> =>
	callApi({
		method: 'POST',
		url: BLOCK_API_CONFIG.BLOCKS_ENDPOINT,
		data: blockData,
		section: BLOCK_API_CONFIG.SECTION,
	})

export const deleteBlock = async (blockId: string): Promise<any> =>
	callApi({
		method: 'DELETE',
		url: `${BLOCK_API_CONFIG.BLOCKS_ENDPOINT}/${blockId}`,
		section: BLOCK_API_CONFIG.SECTION,
	})
