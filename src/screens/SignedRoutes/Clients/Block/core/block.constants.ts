// API Configuration
export const BLOCK_API_CONFIG = {
	SECTION: 'block',
	BLOCKS_ENDPOINT: '/blocks',
	ARENAS_ENDPOINT: '/courts/arenas',
	REPLAY_DAY_ENDPOINT: '/replay/day',
}

// Text Messages
export const BLOCK_TEXTS = {
	LOADING_BLOCKS: 'Carregando bloqueios...',
	NO_BLOCKS: 'Nenhum bloqueio encontrado',
	ADD_BLOCK: 'Adicionar Bloqueio',
	CREATE_BLOCK_SUCCESS: 'Bloqueio criado com sucesso!',
	DELETE_BLOCK_SUCCESS: 'Bloqueio removido com sucesso!',
	FETCH_ERROR: 'Erro ao buscar dados',
	CREATE_ERROR: 'Erro ao criar bloqueio',
	DELETE_ERROR: 'Erro ao remover bloqueio',
}
