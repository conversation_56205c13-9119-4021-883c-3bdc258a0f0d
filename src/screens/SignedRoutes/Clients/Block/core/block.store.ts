import { create } from 'zustand'
import { ErrorMessage, SuccessMessage } from '../../../../../helpers/HandleMessages'
import { useApi } from '../../../../../stores'
import { BLOCK_API_CONFIG, BLOCK_TEXTS } from './block.constants'
import {
	createBlock as apiCreateBlock,
	deleteBlock as apiDeleteBlock,
	fetchArenas,
	fetchBlocks,
	fetchReplaysByDay,
} from './block.service'

interface BlockState {
	blocks: any[]
	courts: any[]
	days: any[]
	loading: boolean
	modalVisible: boolean
	selectedCourt: string | null
	selectedDay: string | null
	hourInitial: string
	hourFinal: string
	fetchBlocks: () => Promise<void>
	fetchCourts: () => Promise<void>
	fetchDays: (courtId: string) => Promise<void>
	setSelectedCourt: (courtId: string | null) => void
	setSelectedDay: (dayId: string | null) => void
	setHourInitial: (hour: string) => void
	setHourFinal: (hour: string) => void
	openBlockModal: () => void
	closeBlockModal: () => void
	resetForm: () => void
	validateForm: () => boolean
	createBlock: () => Promise<void>
	deleteBlock: (id: string) => Promise<void>
	resetBlockData: () => void
}

export const useBlockStore = create<BlockState>((set, get) => ({
	blocks: [],
	courts: [],
	days: [],
	loading: false,
	modalVisible: false,
	selectedCourt: null,
	selectedDay: null,
	hourInitial: '',
	hourFinal: '',

	fetchBlocks: async () => {
		const { isSectionLoading } = useApi.getState()
		if (isSectionLoading(BLOCK_API_CONFIG.SECTION)) return
		try {
			const data = await fetchBlocks()
			set({ blocks: data })
		} catch (error: any) {
			ErrorMessage({ message: error.response?.data?.message || BLOCK_TEXTS.FETCH_ERROR })
		}
	},

	fetchCourts: async () => {
		const { isSectionLoading } = useApi.getState()
		if (isSectionLoading(BLOCK_API_CONFIG.SECTION)) return
		try {
			const data = await fetchArenas()
			set({ courts: data })
		} catch (error: any) {
			ErrorMessage({ message: error.response?.data?.message || BLOCK_TEXTS.FETCH_ERROR })
		}
	},

	fetchDays: async (courtId: string) => {
		if (!courtId) return
		const { isSectionLoading } = useApi.getState()
		if (isSectionLoading(BLOCK_API_CONFIG.SECTION)) return
		try {
			const data = await fetchReplaysByDay(courtId)
			set({ days: data })
		} catch (error: any) {
			ErrorMessage({ message: error.response?.data?.message || BLOCK_TEXTS.FETCH_ERROR })
		}
	},

	setSelectedCourt: (courtId: string | null) => {
		const { selectedCourt, fetchDays } = get()
		if (courtId !== selectedCourt) {
			set({ selectedCourt: courtId, selectedDay: null })
			if (courtId) {
				fetchDays(courtId)
			} else {
				set({ days: [] })
			}
		}
	},

	setSelectedDay: (dayId: string | null) => {
		set({ selectedDay: dayId })
	},

	setHourInitial: (hour: string) => {
		set({ hourInitial: hour })
	},

	setHourFinal: (hour: string) => {
		set({ hourFinal: hour })
	},

	openBlockModal: () => {
		get().fetchCourts()
		get().resetForm()
		set({ modalVisible: true })
	},

	closeBlockModal: () => {
		set({ modalVisible: false })
	},

	resetForm: () => {
		set({
			selectedCourt: null,
			selectedDay: null,
			hourInitial: '',
			hourFinal: '',
		})
	},

	validateForm: () => {
		const { selectedCourt, selectedDay, hourInitial, hourFinal } = get()
		if (!selectedCourt) {
			ErrorMessage({ message: 'Selecione uma quadra' })
			return false
		}
		if (!selectedDay) {
			ErrorMessage({ message: 'Selecione um dia' })
			return false
		}
		if (!hourInitial || !hourFinal) {
			ErrorMessage({ message: 'Defina os horários de início e fim' })
			return false
		}
		const timePattern = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
		if (!timePattern.test(hourInitial) || !timePattern.test(hourFinal)) {
			ErrorMessage({ message: 'Formato de hora inválido. Use HH:MM' })
			return false
		}
		return true
	},

	createBlock: async () => {
		if (!get().validateForm()) return
		const { selectedCourt, selectedDay, hourInitial, hourFinal, closeBlockModal } = get()
		try {
			await apiCreateBlock({
				id_court: selectedCourt,
				id_video_day: selectedDay,
				hour_initial: hourInitial,
				hour_final: hourFinal,
			})
			SuccessMessage({ message: BLOCK_TEXTS.CREATE_BLOCK_SUCCESS })
			closeBlockModal()
			get().fetchBlocks()
		} catch (error: any) {
			ErrorMessage({ message: error.response?.data?.message || BLOCK_TEXTS.CREATE_ERROR })
		}
	},

	deleteBlock: async (id: string) => {
		try {
			await apiDeleteBlock(id)
			SuccessMessage({ message: BLOCK_TEXTS.DELETE_BLOCK_SUCCESS })
			get().fetchBlocks()
		} catch (error: any) {
			ErrorMessage({ message: error.response?.data?.message || BLOCK_TEXTS.DELETE_ERROR })
		}
	},

	resetBlockData: () => {
		set({
			blocks: [],
			courts: [],
			days: [],
			loading: false,
			modalVisible: false,
			selectedCourt: null,
			selectedDay: null,
			hourInitial: '',
			hourFinal: '',
		})
	},
}))

export default useBlockStore
