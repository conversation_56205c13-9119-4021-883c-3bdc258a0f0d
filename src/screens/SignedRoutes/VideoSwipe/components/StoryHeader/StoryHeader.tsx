import { memo } from 'react'
import { Image, StyleSheet, View } from 'react-native'
import Animated, { SlideInUp, SlideOutUp } from 'react-native-reanimated'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import Text from '../../../../../components/atoms/Text'
import { useTheme } from '../../../../../stores'
import HeaderControls from '../HeaderControls/HeaderControls'
import ProgressIndicator from '../ProgressIndicator/ProgressIndicator'

const ANIMATION_DURATION = 250

interface StoryHeaderProps {
	activeIndex: number
	videosLength: number
	courtName: string
	courtImg: string
	time: string | number
	progressValue: number
	onDownload: () => void
	onClose: () => void
	isLandscape: boolean
	showControls: boolean
}

const StoryHeader = memo(
	({
		activeIndex,
		videosLength,
		courtName,
		courtImg,
		time,
		progressValue,
		onDownload,
		onClose,
		isLandscape,
		showControls,
	}: StoryHeaderProps) => {
		const { top } = useSafeAreaInsets()
		const { theme } = useTheme()
		const styles = createStyle(theme, isLandscape)
		if (!showControls) return null
		return (
			<Animated.View
				style={[styles.header, { paddingTop: isLandscape ? theme.spacing.s1 : top }]}
				entering={SlideInUp.duration(ANIMATION_DURATION).springify().damping(15)}
				exiting={SlideOutUp.duration(ANIMATION_DURATION)}
			>
				<ProgressIndicator total={videosLength} activeIndex={activeIndex} progressValue={progressValue} />
				<View style={{ flexDirection: 'row' }}>
					<View style={styles.infoContainer}>
						<Image
							source={{ uri: `https://api.oolhonolance.com.br/img/arenas/${courtImg}.png?hash=true` }}
							style={styles.profileImage}
						/>
						<View>
							<Text style={styles.headerName}>{courtName}</Text>
							<Text size={'small'}>{time}</Text>
						</View>
					</View>
					<HeaderControls onDownload={onDownload} onClose={onClose} />
				</View>
			</Animated.View>
		)
	}
)

const createStyle = (theme: any, isLandscape: boolean) =>
	StyleSheet.create({
		header: {
			position: 'absolute',
			top: 0,
			left: 0,
			right: 0,
			zIndex: 999,
			paddingHorizontal: theme.spacing.s2,
			paddingVertical: theme.spacing.s1,
			gap: theme.spacing.s2,
			backgroundColor: 'rgba(0,0,0,0.7)',
			justifyContent: 'flex-end',
		},
		infoContainer: {
			flex: 1,
			flexDirection: 'row',
			gap: theme.spacing.s1 / 2,
			alignItems: 'center',
		},
		profileImage: {
			width: 30,
			height: 30,
			borderRadius: 15,
		},
		headerName: {
			fontWeight: '600',
		},
	})

export default StoryHeader
