import { RefObject, memo } from 'react'
import { View } from 'react-native'
import { Slider } from 'react-native-awesome-slider'
import Animated, { SlideInDown, SlideOutDown } from 'react-native-reanimated'
import Text from '../../../../../components/atoms/Text'
import { useTheme } from '../../../../../stores'
import { useVideoProgress } from '../../core/videoswipe.hook'
import { createStyle } from './styles'
import { formatTime } from './utils'

const ANIMATION_DURATION = 250

interface VideoProgressBarProps {
	videoRef: RefObject<any>
	videoUri: string
	showControls: boolean
	isLandscape?: boolean
}

const VideoProgressBar = memo(({ videoRef, videoUri, showControls, isLandscape = false }: VideoProgressBarProps) => {
	const { theme } = useTheme()
	const styles = createStyle(theme)
	const { duration, currentTime, progress, min, max, handleSeek } = useVideoProgress(videoRef, videoUri)
	if (!showControls) return null
	return (
		<Animated.View
			style={styles.videoProgressContainer}
			entering={SlideInDown.duration(ANIMATION_DURATION).springify().damping(15)}
			exiting={SlideOutDown.duration(ANIMATION_DURATION)}
		>
			<View style={styles.timeTextContainer}>
				<Text style={styles.timeText} numberOfLines={1}>
					{formatTime(currentTime)}
				</Text>
			</View>
			<View style={styles.sliderContainer}>
				<Slider
					style={styles.slider}
					progress={progress}
					minimumValue={min}
					maximumValue={max}
					thumbWidth={12}
					sliderHeight={4}
					bubble={(value) => formatTime(value * duration)}
					onSlidingStart={() => {
						if (videoRef.current) videoRef.current.pauseAsync()
					}}
					onSlidingComplete={(value) => {
						handleSeek(value)
						if (videoRef.current) videoRef.current.playAsync()
					}}
					theme={{
						minimumTrackTintColor: theme.colors.primary,
						maximumTrackTintColor: 'rgba(255, 255, 255, 0.3)',
						bubbleBackgroundColor: 'rgba(0, 0, 0, 0.8)',
						thumbTintColor: theme.colors.primary,
					}}
				/>
			</View>
			<View style={styles.timeTextContainer}>
				<Text style={styles.timeText} numberOfLines={1}>
					{formatTime(duration)}
				</Text>
			</View>
		</Animated.View>
	)
})

export default VideoProgressBar
