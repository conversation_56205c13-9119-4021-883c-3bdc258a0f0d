import { StyleSheet } from 'react-native'

export const createStyle = (theme: any) =>
	StyleSheet.create({
		videoProgressContainer: {
			position: 'absolute',
			bottom: 0,
			left: 0,
			right: 0,
			flexDirection: 'row',
			alignItems: 'center',
			zIndex: 1,
			backgroundColor: 'rgba(0, 0, 0, 0.7)',
			height: 45,
		},
		sliderContainer: {
			flex: 1,
			justifyContent: 'center',
		},
		slider: {
			width: '100%',
		},
		timeTextContainer: {
			justifyContent: 'center',
			overflow: 'hidden',
			paddingHorizontal: theme.spacing.s2,
		},
		timeText: {
			color: theme.colors.white,
			fontSize: 12,
			textAlign: 'center',
		},
	})
