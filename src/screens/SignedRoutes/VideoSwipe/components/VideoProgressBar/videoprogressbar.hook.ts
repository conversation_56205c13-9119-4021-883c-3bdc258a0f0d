import { useEffect } from 'react'
import { interpolate, useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated'

export const useProgressBarAnimations = (showControls: boolean, isLandscape: boolean, controlsOpacityProp: number) => {
	const ANIMATION_DURATION = 300

	const sliderOpacity = useSharedValue(1)
	const controlsOpacity = useSharedValue(controlsOpacityProp === 0 ? 0 : 1)
	const fadeAnimation = useSharedValue(1)

	useEffect(() => {
		controlsOpacity.value = controlsOpacityProp === 0 ? 0 : 1
	}, [controlsOpacityProp])

	useEffect(() => {
		sliderOpacity.value = withTiming(showControls ? 1 : 0.5, {
			duration: ANIMATION_DURATION,
		})
	}, [showControls])

	useEffect(() => {
		fadeAnimation.value = withTiming(showControls ? 1 : 0, {
			duration: ANIMATION_DURATION,
		})
	}, [showControls])

	const containerAnimatedStyle = useAnimatedStyle(() => ({
		height: 56,
		backgroundColor: withTiming(showControls && !isLandscape ? 'rgba(0, 0, 0, 0.5)' : 'rgba(0, 0, 0, 0.2)', {
			duration: ANIMATION_DURATION,
		}),
		paddingVertical: withTiming(showControls && !isLandscape ? 8 : 2, { duration: ANIMATION_DURATION }),
		transform: [
			{
				translateY: interpolate(fadeAnimation.value, [0, 1], [isLandscape ? 56 : 30, 0]),
			},
		],
	}))

	const timeTextAnimatedStyle = useAnimatedStyle(() => ({
		opacity: fadeAnimation.value,
		transform: [
			{
				translateY: interpolate(fadeAnimation.value, [0, 1], [10, 0]),
			},
		],
		width: withTiming(showControls ? 35 : 0, { duration: ANIMATION_DURATION }),
		height: withTiming(showControls ? 50 : 0, { duration: ANIMATION_DURATION }),
	}))

	const sliderAnimatedStyle = useAnimatedStyle(() => ({
		flex: 1,
		height: withTiming(showControls ? 40 : 4, { duration: ANIMATION_DURATION }),
		marginHorizontal: withTiming(showControls ? 8 : 0, { duration: ANIMATION_DURATION }),
		opacity: withTiming(showControls ? 1 : 0.7, { duration: ANIMATION_DURATION }),
	}))

	return {
		containerAnimatedStyle,
		timeTextAnimatedStyle,
		sliderAnimatedStyle,
	}
}
