import { memo } from 'react'
import { StyleSheet, View } from 'react-native'
import Icon from '../../../../../components/atoms/Icon/Icon'
import { useTheme } from '../../../../../stores'

interface HeaderControlsProps {
	onDownload?: () => void
	onClose?: () => void
}

const HeaderControls = memo(({ onDownload, onClose }: HeaderControlsProps) => {
	const { theme } = useTheme()
	const styles = createStyle(theme)

	return (
		<View style={styles.containerButtons}>
			{onDownload && (
				<Icon
					library={'AntDesign'}
					name={'download'}
					size={theme.spacing.s2}
					color={theme.colors.text}
					onPress={onDownload}
				/>
			)}
			{onClose && (
				<Icon
					library={'EvilIcons'}
					name={'close'}
					size={theme.spacing.s4}
					color={theme.colors.white}
					onPress={onClose}
				/>
			)}
		</View>
	)
})

const createStyle = (theme: any) =>
	StyleSheet.create({
		containerButtons: {
			flexDirection: 'row',
			alignItems: 'center',
			gap: theme.spacing.s2,
		},
	})

export default HeaderControls
