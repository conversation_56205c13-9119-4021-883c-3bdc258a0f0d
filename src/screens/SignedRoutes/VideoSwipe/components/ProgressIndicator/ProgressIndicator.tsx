import { memo, useEffect, useRef } from 'react'
import { Animated, StyleSheet, View } from 'react-native'
import { useTheme } from '../../../../../stores'

interface ProgressIndicatorProps {
	total: number
	activeIndex: number
	progressValue: number
}

const ProgressIndicator = memo(({ total, activeIndex, progressValue }: ProgressIndicatorProps) => {
	const { theme } = useTheme()
	const styles = createStyle(theme)

	const renderProgressBar = (index: number) => {
		if (index < activeIndex) {
			return <View style={[styles.activeProgress, { width: '100%' }]} />
		}
		if (index === activeIndex) {
			const fillWidth = useRef(new Animated.Value(progressValue * 100)).current
			useEffect(() => {
				Animated.timing(fillWidth, {
					toValue: progressValue * 100,
					duration: 100,
					useNativeDriver: false,
				}).start()
			}, [progressValue])
			return (
				<Animated.View
					style={[
						styles.activeProgress,
						{
							width: fillWidth.interpolate({
								inputRange: [0, 100],
								outputRange: ['0%', '100%'],
							}),
						},
					]}
				/>
			)
		}
		return <View style={styles.inactiveProgress} />
	}

	return (
		<View style={styles.progressContainer}>
			{Array.from({ length: total }).map((_, i) => (
				<View key={i} style={styles.progressBar}>
					{renderProgressBar(i)}
				</View>
			))}
		</View>
	)
})

const createStyle = (theme: any) =>
	StyleSheet.create({
		progressContainer: {
			flexDirection: 'row',
			height: 3,
			width: '100%',
			marginTop: theme.spacing.s1,
		},
		progressBar: {
			flex: 1,
			height: '100%',
			backgroundColor: 'rgba(255, 255, 255, 0.3)',
			marginHorizontal: 2,
			position: 'relative',
			overflow: 'hidden',
			borderRadius: 1.5,
		},
		activeProgress: {
			height: '100%',
			backgroundColor: theme.colors.primary,
			borderRadius: 1.5,
		},
		inactiveProgress: {
			height: '100%',
			backgroundColor: 'rgba(255, 255, 255, 0.3)',
		},
	})

export default ProgressIndicator
