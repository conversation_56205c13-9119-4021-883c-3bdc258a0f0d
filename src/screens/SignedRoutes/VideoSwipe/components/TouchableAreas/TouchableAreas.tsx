import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import { Icon } from '../../../../../components'
import { useTheme } from '../../../../../stores'

interface TouchableAreasProps {
	onPrevious: () => void
	onNext: () => void
	onCenterPress?: () => void
	disabled?: boolean
	showTutorial?: boolean
	onTutorialPress?: () => void
}

const TouchableAreas = ({
	onPrevious,
	onNext,
	onCenterPress,
	disabled,
	showTutorial,
	onTutorialPress,
}: TouchableAreasProps) => {
	const { theme } = useTheme()
	const styles = createStyle(theme)

	const handlePress = (action?: () => void) => {
		if (showTutorial && onTutorialPress) {
			onTutorialPress()
			return
		}
		if (typeof action === 'function') {
			action()
		}
	}

	return (
		<View style={styles.container}>
			<TouchableOpacity
				style={[styles.previousArea, styles.touchArea]}
				onPress={() => handlePress(onPrevious)}
				disabled={disabled}
			>
				{showTutorial && (
					<View style={styles.tutorialBox}>
						<Icon
							library={'MaterialCommunityIcons'}
							name={'gesture-tap'}
							size={30}
							color={theme.colors.white}
						/>
						<Text style={[styles.tutorialText, { color: theme.colors.white }]}>Toque aqui para voltar</Text>
					</View>
				)}
			</TouchableOpacity>
			<TouchableOpacity
				style={[styles.centerArea, styles.touchArea]}
				onPress={() => handlePress(onCenterPress)}
				disabled={disabled}
			/>
			<TouchableOpacity
				style={[styles.nextArea, styles.touchArea]}
				onPress={() => handlePress(onNext)}
				disabled={disabled}
			>
				{showTutorial && (
					<View style={styles.tutorialBox}>
						<Icon
							library={'MaterialCommunityIcons'}
							name={'gesture-tap'}
							size={30}
							color={theme.colors.white}
						/>
						<Text style={[styles.tutorialText, { color: theme.colors.white }]}>
							Toque aqui para avançar
						</Text>
					</View>
				)}
			</TouchableOpacity>
		</View>
	)
}

const createStyle = (theme: any) =>
	StyleSheet.create({
		container: {
			position: 'absolute',
			top: 0,
			left: 0,
			right: 0,
			bottom: 70,
			flexDirection: 'row',
			zIndex: 100,
		},
		touchArea: {
			height: '100%',
		},
		previousArea: {
			flex: 0.3,
		},
		centerArea: {
			flex: 0.4,
		},
		nextArea: {
			flex: 0.3,
		},
		tutorialBox: {
			position: 'absolute',
			backgroundColor: 'rgba(0, 0, 0, 0.7)',
			height: '100%',
			width: '100%',
			top: 0,
			left: 0,
			right: 0,
			justifyContent: 'center',
			alignItems: 'center',
			gap: theme.spacing.s1,
			zIndex: 1000,
			elevation: 1000,
		},
		tutorialText: {
			fontSize: theme.fontSize.medium,
			textAlign: 'center',
			fontWeight: 'bold',
		},
	})

export default TouchableAreas
