import { useNavigation } from '@react-navigation/native'
import { Video } from 'expo-av'
import * as NavigationBar from 'expo-navigation-bar'
import * as ScreenOrientation from 'expo-screen-orientation'
import { memo, useCallback, useEffect, useRef, useState } from 'react'
import { Dimensions, PanResponder, Platform, StatusBar, StyleSheet, UIManager, View } from 'react-native'
import Animated, { runOnJS, useAnimatedStyle, useSharedValue, withSpring, withTiming } from 'react-native-reanimated'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { enableScreens } from 'react-native-screens'
import { getLocalItem, setLocalItem } from '../../../helpers/AsyncStorage'
import { downloadFile } from '../../../helpers/DownloadFile'
import { useTheme } from '../../../stores'
import StoryHeader from './components/StoryHeader/StoryHeader'
import TouchableAreas from './components/TouchableAreas/TouchableAreas'
import VideoProgressBar from './components/VideoProgressBar/VideoProgressBar'
import { useVideoProgress } from './core/videoswipe.hook'

enableScreens()

const TUTORIAL_STORAGE_KEY = '@olhonolance:video-tutorial-viewed'
const SCREEN_HEIGHT = Dimensions.get('window').height

interface VideoSwipeProps {
	route: {
		params: {
			videos: any[]
			selectedVideo: any
			nameArena: string
			courtImg?: string
		}
	}
}

const VideoSwipe = ({ route: { params } }: VideoSwipeProps) => {
	const { videos, selectedVideo, nameArena, courtImg } = params
	const { theme } = useTheme()
	const [isLandscape, setIsLandscape] = useState(false)
	const [showTutorial, setShowTutorial] = useState(false)
	const [showControls, setShowControls] = useState(true)
	const translateY = useSharedValue(0)
	const opacity = useSharedValue(1)
	const insets = useSafeAreaInsets()
	const styles = VideoSwipe.styles(theme, isLandscape, insets)
	const navigation = useNavigation()

	const animatedStyle = useAnimatedStyle(() => ({
		transform: [{ translateY: translateY.value }],
		opacity: opacity.value,
	}))

	const panResponder = useRef(
		PanResponder.create({
			onStartShouldSetPanResponder: () => !isLandscape,
			onMoveShouldSetPanResponder: (_, gestureState) => !isLandscape && gestureState.dy > 10,
			onPanResponderMove: (_, gestureState) => {
				if (gestureState.dy > 0 && !isLandscape) {
					translateY.value = gestureState.dy
					const newOpacity = 1 - gestureState.dy / (SCREEN_HEIGHT * 0.4)
					opacity.value = newOpacity > 0 ? newOpacity : 0
				}
			},
			onPanResponderRelease: (_, gestureState) => {
				if (gestureState.dy > SCREEN_HEIGHT * 0.2 && !isLandscape) {
					translateY.value = withTiming(SCREEN_HEIGHT, { duration: 250 }, () => {
						runOnJS(handleGoBack)()
					})
					opacity.value = withTiming(0, { duration: 250 })
				} else {
					translateY.value = withSpring(0, { stiffness: 120, damping: 10 })
					opacity.value = withTiming(1, { duration: 200 })
				}
			},
		})
	).current

	useEffect(() => {
		if (Platform.OS === 'android') NavigationBar.setBackgroundColorAsync(theme.colors.black)
		return () => Platform.OS === 'android' && NavigationBar.setBackgroundColorAsync(theme.colors.background)
	}, [])

	useEffect(() => {
		const setupOrientation = async () => {
			await ScreenOrientation.unlockAsync()
		}
		setupOrientation()
		const subscription = ScreenOrientation.addOrientationChangeListener(({ orientationInfo }) => {
			const isLandscapeMode =
				orientationInfo.orientation === ScreenOrientation.Orientation.LANDSCAPE_LEFT ||
				orientationInfo.orientation === ScreenOrientation.Orientation.LANDSCAPE_RIGHT
			setIsLandscape(isLandscapeMode)
		})
		if (Platform.OS === 'android') {
			if (UIManager.setLayoutAnimationEnabledExperimental) {
				UIManager.setLayoutAnimationEnabledExperimental(true)
			}
		}
		return () => {
			ScreenOrientation.removeOrientationChangeListener(subscription)
			ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP)
		}
	}, [])

	useEffect(() => {
		const checkTutorialStatus = async () => {
			const tutorialViewed = await getLocalItem(TUTORIAL_STORAGE_KEY)
			setShowTutorial(!tutorialViewed)
		}
		checkTutorialStatus()
	}, [])

	const handleGoBack = useCallback(async () => {
		await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP)
		navigation.goBack()
	}, [navigation])

	const handleCloseTutorial = useCallback(async () => {
		setShowTutorial(false)
		await setLocalItem(TUTORIAL_STORAGE_KEY, 'true')
	}, [])

	const handleCenterPress = useCallback(() => {
		setShowControls((prev) => !prev)
	}, [])

	const { currentIndex, setCurrentIndex, progress, time, setTime, handleVideoProgress, resetProgress } =
		useVideoProgress(videos, selectedVideo, () => {
			if (currentIndex < videos.length - 1) {
				setCurrentIndex((prev: number) => prev + 1)
				setTime(videos[currentIndex + 1].time_video)
				resetProgress()
			} else {
				navigation.goBack()
			}
		})

	const handlePreviousVideo = useCallback(() => {
		if (currentIndex > 0) {
			setCurrentIndex((prev: number) => prev - 1)
			setTime(videos[currentIndex - 1].time_video)
			resetProgress()
		}
	}, [currentIndex, time])

	if (!videos.length || !selectedVideo) return navigation.goBack()

	return (
		<Animated.View {...panResponder.panHandlers} style={[styles.container, animatedStyle]}>
			<View style={styles.safeArea}>
				{!showTutorial && (
					<StoryHeader
						activeIndex={currentIndex}
						videosLength={videos.length}
						courtName={nameArena}
						courtImg={courtImg}
						time={time}
						progressValue={progress}
						onClose={handleGoBack}
						onDownload={() => downloadFile(videos[currentIndex].link_amazon_video, () => {})}
						isLandscape={isLandscape}
						showControls={showControls}
					/>
				)}
				<View style={styles.videoContainer}>
					<TouchableAreas
						onPrevious={handlePreviousVideo}
						onNext={() => {
							if (currentIndex < videos.length - 1) {
								setCurrentIndex((prev: number) => prev + 1)
								setTime(videos[currentIndex + 1].time_video)
								resetProgress()
							} else {
								navigation.goBack()
							}
						}}
						disabled={!videos.length}
						showTutorial={showTutorial}
						onTutorialPress={handleCloseTutorial}
						onCenterPress={handleCenterPress}
					/>
					<StoryContent
						videoUri={videos[currentIndex].link_amazon_video}
						onVideoEnd={() => {
							if (currentIndex < videos.length - 1) {
								setCurrentIndex((prev: number) => prev + 1)
								setTime(videos[currentIndex + 1].time_video)
								resetProgress()
							} else {
								navigation.goBack()
							}
						}}
						onStatusUpdate={handleVideoProgress}
						isLandscape={isLandscape}
						showTutorial={showTutorial}
						showControls={showControls}
					/>
				</View>
				<StatusBar hidden={isLandscape} backgroundColor={theme.colors.black} />
			</View>
		</Animated.View>
	)
}

interface StoryContentProps {
	videoUri: string
	onStatusUpdate: (status: any) => void
	isLandscape: boolean
	showTutorial: boolean
	showControls: boolean
}

const StoryContent = memo(
	({ videoUri, onStatusUpdate, isLandscape, showTutorial, showControls }: StoryContentProps) => {
		const { theme } = useTheme()
		const styles = VideoSwipe.styles(theme, isLandscape)
		const videoRef = useRef<any>(null)

		useEffect(() => {
			if (videoRef.current) {
				if (showTutorial) {
					videoRef.current.pauseAsync()
				} else {
					videoRef.current.playAsync()
				}
			}
		}, [showTutorial])

		const onFullscreenUpdate = useCallback(async ({ fullscreenUpdate }: any) => {
			if (fullscreenUpdate === Video.FULLSCREEN_UPDATE_PLAYER_DID_PRESENT) {
				await ScreenOrientation.unlockAsync()
			} else if (fullscreenUpdate === Video.FULLSCREEN_UPDATE_PLAYER_WILL_DISMISS) {
				await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP)
			}
		}, [])

		return (
			<View style={styles.videoWrapper}>
				<Video
					ref={videoRef}
					key={videoUri}
					source={{ uri: videoUri }}
					style={styles.video}
					useNativeControls={false}
					shouldPlay={!showTutorial}
					isLooping={false}
					resizeMode={'contain'}
					onPlaybackStatusUpdate={onStatusUpdate}
					onFullscreenUpdate={onFullscreenUpdate}
				/>
				<VideoProgressBar
					videoRef={videoRef}
					videoUri={videoUri}
					showControls={showControls && !showTutorial}
				/>
			</View>
		)
	}
)

VideoSwipe.styles = (theme: any, isLandscape: boolean, insets?: any) => {
	insets = insets || useSafeAreaInsets()
	return StyleSheet.create({
		container: {
			flex: 1,
			backgroundColor: theme.colors.black,
		},
		safeArea: {
			flex: 1,
			paddingTop: insets.top,
			paddingBottom: insets.bottom,
			paddingLeft: insets.left,
			paddingRight: insets.right,
		},
		videoContainer: {
			flex: 1,
			position: 'relative',
			...(isLandscape && {
				position: 'absolute',
				top: 0,
				left: 0,
				right: 0,
				bottom: 0,
				backgroundColor: theme.colors.black,
				zIndex: 999,
			}),
		},
		video: {
			width: '100%',
			height: '100%',
			borderTopRightRadius: isLandscape ? 0 : theme.spacing.s1,
			borderTopLeftRadius: isLandscape ? 0 : theme.spacing.s1,
		},
		videoWrapper: {
			flex: 1,
			justifyContent: 'center',
		},
	})
}

export default VideoSwipe
