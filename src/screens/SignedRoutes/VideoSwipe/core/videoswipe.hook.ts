import { useCallback, useMemo, useState } from 'react'

interface Video {
	id_video: string | number
	time_video: number
	[key: string]: any
}

interface Status {
	isLoaded: boolean
	durationMillis: number
	positionMillis: number
	didJustFinish: boolean
}

export const useVideoProgress = (videos: Video[], selectedVideo: Video, onVideoEnd: () => void) => {
	const startIndex = useMemo(
		() => videos.findIndex((v) => v.id_video === selectedVideo.id_video),
		[videos, selectedVideo]
	)
	const [currentIndex, setCurrentIndex] = useState<number>(startIndex)
	const [progress, setProgress] = useState<number>(0)
	const [time, setTime] = useState<number>(selectedVideo.time_video)

	const handleVideoProgress = useCallback(
		(status: Status) => {
			if (status.isLoaded && status.durationMillis) {
				setProgress(status.positionMillis / status.durationMillis)
				if (status.didJustFinish) onVideoEnd()
			}
		},
		[onVideoEnd]
	)

	const resetProgress = useCallback(() => {
		setProgress(0)
	}, [])

	return {
		currentIndex,
		setCurrentIndex,
		progress,
		time,
		setTime,
		handleVideoProgress,
		resetProgress,
	}
}
