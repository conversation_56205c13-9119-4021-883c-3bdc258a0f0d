import { ActivityIndicator, Image, StyleSheet, View } from 'react-native'

import { useTheme } from '../../stores'

import Logo from '../../../assets/logo.png'

const SplashScreen = () => {
	const { theme } = useTheme()

	const styles = createStyle(theme)

	return (
		<View style={styles.container}>
			<View style={styles.content}>
				<Image source={Logo} style={styles.logo} resizeMode={'contain'} />
				<ActivityIndicator size={'large'} color={theme.colors.primary} style={styles.loader} />
			</View>
		</View>
	)
}

const createStyle = (theme: any) =>
	StyleSheet.create({
		container: {
			flex: 1,
			justifyContent: 'center',
			alignItems: 'center',
			backgroundColor: theme.colors.background,
		},
		content: {
			alignItems: 'center',
			justifyContent: 'center',
		},
		logo: {
			width: 120,
			height: 120,
			marginBottom: theme.spacing.s4,
		},
		loader: {
			marginTop: theme.spacing.s2,
		},
	})

export default SplashScreen
