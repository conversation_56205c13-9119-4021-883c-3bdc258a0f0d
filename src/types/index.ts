// Tipos para ícones
export interface IconProps {
	library:
		| 'AntDesign'
		| 'Entypo'
		| 'EvilIcons'
		| 'Feather'
		| 'FontAwesome'
		| 'FontAwesome5'
		| 'FontAwesome6'
		| 'Fontisto'
		| 'Ionicons'
		| 'MaterialCommunityIcons'
		| 'MaterialIcons'
		| 'Octicons'
		| 'SimpleLineIcons'
	name: string
	size?: string | number
	color?: string
	onPress?: () => void
}

// Tipos para Arena
export interface Arena {
	id_arena: string
	arena: string
	city: string
	name: string
}

// Tipos para Dashboard
export interface DashboardVideo {
	id: string
	arena: string
	link_amazon_thumb: string
	court: string
	time_video: string
	title?: string
	thumbnail?: string
	created_at?: string
	[key: string]: any
}

export interface DashboardData {
	arenas: number
	courts_active: number
	log_download: number
	videos: number
	last_videos: DashboardVideo[]
}

// Tipos para Replays
export interface ReplayVideo {
	id: string
	link_amazon?: string
	link_amazon_thumb?: string
	camera2?: ReplayVideo
	court?: string
	arena?: string
	time_video?: string
	[key: string]: any
}

export interface ReplayField {
	id: string
	name: string
	[key: string]: any
}

export interface ReplayDay {
	id: string
	date: string
	[key: string]: any
}

export interface ReplayHour {
	id: string
	time: string
	[key: string]: any
}

// Tipos para AsyncStorage
export type StorageKey = string

export interface AsyncStorageHelpers {
	setLocalItem: (key: StorageKey, value: any) => Promise<boolean>
	getLocalItem: (key: StorageKey) => Promise<any>
	deleteLocalItem: (key: StorageKey) => Promise<void>
}

// Tipos para Tutorial
export interface TutorialHandlers {
	checkTutorial: (storageKey: StorageKey) => Promise<void>
	handleTutorialDone: (storageKey: StorageKey) => Promise<void>
}

// Tipos para tema
export interface ThemeColors {
	primary: string
	primary50: string
	secondary: string
	secondary50: string
	tertiary: string
	tertiary50: string
	background: string
	text: string
	white: string
	black: string
	blue: string
	textLight: string
	red: string
	yellow: string
	base100: string
	base200: string
	base300: string
	base400: string
	error: string
	textBody?: string
}

export interface FontSizes {
	extraSmall: number
	small: number
	medium: number
	large: number
	extraLarge: number
}

export interface ThemeSpacing {
	s1: number
	s2: number
	s3: number
	s4: number
	s5: number
	s6: number
	s7: number
	s8: number
	s9: number
	s10: number
}

export interface Theme {
	type: 'dark' | 'light'
	colors: ThemeColors
	fontSize: FontSizes
	spacing: ThemeSpacing
}

// Tipos para usuário
export interface User {
	id?: number | string
	email: string
	name?: string
	token?: string
	access_token?: string
	password?: string
	[key: string]: any
}

// Tipos para componentes
export interface ButtonProps {
	type?: 'text' | 'outlined' | 'contained'
	size?: 'small' | 'medium' | 'large'
	loading?: boolean
	label?: string
	icon?: IconProps
	onPress?: () => void
	style?: any
	textStyle?: any
	colors?: string[]
	children?: React.ReactNode
	testID?: string
	disabled?: boolean
	accessibilityLabel?: string
	[key: string]: any
}

export interface TextProps {
	size?: keyof FontSizes
	style?: any
	children?: React.ReactNode
	[key: string]: any
}

export interface TextInputProps {
	name?: string
	icon?: IconProps
	iconPosition?: 'left' | 'right'
	label?: string
	value?: string
	style?: any
	placeholder?: string
	containerStyle?: any
	secureTextEntry?: boolean
	mask?: 'whatsapp' | string
	error?: string
	onChangeText?: (text: string, rawValue?: string) => void
	keyboardType?: any
	autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters'
	autoCompleteType?: string
	textContentType?: string
	testID?: string
	[key: string]: any
}

export interface CheckProps {
	checked?: boolean
	label?: string
	onPress?: (checked: boolean) => void
	style?: any
	[key: string]: any
}

export interface ItemProps {
	active?: boolean
	name: string
	image?: string
	icon?: {
		library: string
		name: string
		size?: 'extraSmall' | 'small' | 'medium' | 'large' | 'extraLarge' | number
		color?: string
		style?: any
	}
	onPressIcon?: () => void
	imageStyle?: any
	video?: any
	style?: any
	containerStyle?: any
	aspectRatio?: number
	onPress?: () => void
	isSelectionMode?: boolean
	[key: string]: any
}

export interface BoxProps {
	id?: string | number
	label: string
	value: string | number
	onPress?: (id?: string | number) => void
	[key: string]: any
}

export interface VideoPlayerProps {
	url: string
	style?: any
	onChangeCamera?: () => void
	onShare?: () => void
	onDownload?: () => void
	onExpand?: () => void
	[key: string]: any
}

interface ActionButtonProps {
	onPress: () => void
	icon: {
		library: string
		name: string
		size?: number
		color?: string
	}
	position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
	disabled?: boolean
}

export interface ScrollHorizontalProps {
	items: any[]
	active: string | number
	idxName: string
	idxItem: string
	onPress?: (itemId: string | number) => void
	idxImage?: string
	loading?: boolean
	onPressIcone?: (item: any) => void
	imageStyle?: any
	style?: any
	icon?: {
		library: string
		name: string
		size?: number
		color?: string
		style?: object
	}
	favoriteItems?: any[]
	favoriteIdxItem?: string
}

export interface ModalProps {
	visible?: boolean
	style?: any
	onDismiss?: () => void
	children?: React.ReactNode
	[key: string]: any
}

export interface ToastMessagesProps {
	type: 'error' | 'success' | 'info'
	text1?: {
		text?: string
		icon?: {
			name: string
			library?: string
			size?: string | number
			color?: string
		}
	}
	text2?: {
		text?: string
		icon?: {
			name: string
			library?: string
			size?: string | number
			color?: string
		}
	}
	[key: string]: any
}

// Tipos para stores
export interface UserStore {
	user: User | null
	setUser: (userData: User) => void
	clearUser: () => void
	updateUser: (updates: Partial<User>) => void
}

export interface UserComputed extends UserStore {
	signed: boolean
}

export interface ThemeStore {
	theme: Theme
	toggleTheme: () => void
}

export interface ApiStore {
	sectionLoading: Record<string, boolean>
	callApi: (config: ApiConfig) => Promise<any>
	resetApiData: () => void
}

export interface ApiConfig {
	method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
	section?: string
	url: string
	body?: any
	headers?: Record<string, string>
	config?: Record<string, any>
	background?: boolean
}

// Tipos específicos para Login
export interface LoginFormData {
	email: string
	password: string
	remember: boolean
	passwordVisible: boolean
}

export interface LoginStore extends LoginFormData {
	updateField: (field: keyof LoginFormData, value: any) => void
	toggleField: (field: keyof LoginFormData) => void
	resetForm: () => void
	setUserData: (userData: Partial<User>) => void
}

export interface LoginValidation {
	isValid: boolean
	message: string | null
}

export interface LoginFormConfig {
	email: {
		label: string
		placeholder: string
		field: string
		keyboardType: string
		autoCapitalize: string
	}
	password: {
		label: string
		placeholder: string
		field: string
		secureTextEntry: boolean
		autoCapitalize: string
	}
	remember: {
		label: string
		field: string
	}
}

// Exportar todos os tipos de autenticação
export * from './auth'

// Exportar todos os tipos de componentes
export * from './components'
