import { ReactNode } from 'react'
import {
	TextInputProps as RNTextInputProps,
	TextProps as RNTextProps,
	TextStyle,
	TouchableOpacityProps,
	ViewStyle,
} from 'react-native'

// Tipos para o componente Text
export interface TextProps extends RNTextProps {
	size?: 'small' | 'medium' | 'large' | 'extraLarge'
	style?: TextStyle
	children?: ReactNode
}

// Tipos para o componente Button
export interface ButtonProps extends TouchableOpacityProps {
	type?: 'contained' | 'outlined' | 'text'
	size?: 'small' | 'medium' | 'large'
	loading?: boolean
	label?: string
	icon?: {
		name: string
		library?: string
		color?: string
		size?: string | number
		onPress?: () => void
	}
	onPress?: () => void
	style?: ViewStyle
	textStyle?: TextStyle
	colors?: string[]
	children?: ReactNode
	testID?: string
}

// Tipos para o componente TextInput
export interface TextInputProps extends RNTextInputProps {
	label?: string
	placeholder?: string
	value?: string
	onChangeText?: (text: string) => void
	secureTextEntry?: boolean
	iconPosition?: 'left' | 'right'
	icon?: {
		name: string
		library?:
			| 'AntDesign'
			| 'Entypo'
			| 'EvilIcons'
			| 'Feather'
			| 'FontAwesome'
			| 'FontAwesome5'
			| 'FontAwesome6'
			| 'Fontisto'
			| 'Ionicons'
			| 'MaterialCommunityIcons'
			| 'MaterialIcons'
			| 'Octicons'
			| 'SimpleLineIcons'
		color?: string
		size?: string | number
		onPress?: () => void
	}
	error?: string | null
	mask?: 'whatsapp' | 'cpf' | 'cnpj' | 'phone' | 'currency' | string
	keyboardType?:
		| 'default'
		| 'email-address'
		| 'numeric'
		| 'phone-pad'
		| 'number-pad'
		| 'decimal-pad'
		| 'visible-password'
		| 'ascii-capable'
		| 'numbers-and-punctuation'
		| 'url'
		| 'name-phone-pad'
		| 'twitter'
		| 'web-search'
	autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters'
	autoCompleteType?: 'name' | 'email' | 'password' | 'tel' | 'username' | 'off'
	textContentType?:
		| 'none'
		| 'URL'
		| 'addressCity'
		| 'addressCityAndState'
		| 'addressState'
		| 'countryName'
		| 'creditCardNumber'
		| 'emailAddress'
		| 'familyName'
		| 'fullStreetAddress'
		| 'givenName'
		| 'jobTitle'
		| 'location'
		| 'middleName'
		| 'name'
		| 'namePrefix'
		| 'nameSuffix'
		| 'nickname'
		| 'organizationName'
		| 'postalCode'
		| 'streetAddressLine1'
		| 'streetAddressLine2'
		| 'sublocality'
		| 'telephoneNumber'
		| 'username'
		| 'password'
		| 'newPassword'
		| 'oneTimeCode'
	testID?: string
}

// Tipos para o componente Check
export interface CheckProps {
	checked?: boolean
	onPress?: () => void
	label?: string
	style?: ViewStyle
}

// Tipos para o componente Icon (baseado no que já existe)
export interface IconComponentProps {
	library:
		| 'AntDesign'
		| 'Entypo'
		| 'EvilIcons'
		| 'Feather'
		| 'FontAwesome'
		| 'FontAwesome5'
		| 'FontAwesome6'
		| 'Fontisto'
		| 'Ionicons'
		| 'MaterialCommunityIcons'
		| 'MaterialIcons'
		| 'Octicons'
		| 'SimpleLineIcons'
	name: string
	size?: string | number
	color?: string
	onPress?: () => void
	style?: any
}
