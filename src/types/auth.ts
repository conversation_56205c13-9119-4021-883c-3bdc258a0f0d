// Tipos para autenticação

// Enums para melhor type safety
export enum AuthRoute {
	LOGIN = 'Login',
	SIGN_UP = 'SignUp',
	FORGOT_PASSWORD = 'ForgotPassword',
}

export enum KeyboardType {
	DEFAULT = 'default',
	EMAIL_ADDRESS = 'email-address',
	NUMERIC = 'numeric',
	PHONE_PAD = 'phone-pad',
	NUMBER_PAD = 'number-pad',
	DECIMAL_PAD = 'decimal-pad',
	VISIBLE_PASSWORD = 'visible-password',
	ASCII_CAPABLE = 'ascii-capable',
	NUMBERS_AND_PUNCTUATION = 'numbers-and-punctuation',
	URL = 'url',
	NAME_PHONE_PAD = 'name-phone-pad',
	TWITTER = 'twitter',
	WEB_SEARCH = 'web-search',
}

export enum AutoCapitalizeType {
	NONE = 'none',
	SENTENCES = 'sentences',
	WORDS = 'words',
	CHARACTERS = 'characters',
}
export interface LoginFormData {
	email: string
	password: string
	remember: boolean
}

export interface SignUpFormData {
	name: string
	email: string
	password: string
	passwordConfirmation: string
	whatsapp: string
	terms: boolean
}

export interface ForgotPasswordFormData {
	email: string
}

export interface ProfileFormData {
	name: string
	email: string
	password: string
	confirmPassword: string
	whatsapp: string
}

export interface User {
	id_user: number
	name: string
	email: string
	whatsapp?: string
	access_token?: string
	token?: string
}

export interface AuthResponse {
	access_token: string
	message?: string
	user?: User
	[key: string]: any
}

export interface LoginState {
	email: string
	password: string
	remember: boolean
	passwordVisible: boolean
}

export interface SignUpState {
	name: string
	email: string
	password: string
	passwordConfirmation: string
	whatsapp: string
	whatsappFormatted: string
	terms: boolean
	showPasswords: boolean
	loading: boolean
	errors: {
		name: string | null
		email: string | null
		password: string | null
		passwordConfirmation: string | null
		whatsapp: string | null
	}
}

export interface ProfileState {
	name: string
	email: string
	password: string
	confirmPassword: string
	whatsapp: string
	showPassword: boolean
	showConfirmPassword: boolean
}

export interface ValidationResult {
	isValid: boolean
	message?: string
	errors?: Record<string, string>
	termsError?: string
}

export interface FormFieldConfig {
	label: string
	placeholder?: string
	keyboardType?: KeyboardType
	autoCapitalize?: AutoCapitalizeType
	autoCompleteType?: string
	textContentType?: string
	mask?: string
}

export interface AuthApiConfig {
	SECTION: string
}

export interface AuthRoutes {
	[key: string]: string
}

export interface AuthTexts {
	[key: string]: string
}

export interface AuthStorageKeys {
	USER_DATA: string
	[key: string]: string
}
