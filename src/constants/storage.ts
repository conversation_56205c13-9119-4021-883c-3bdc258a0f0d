import type { StorageKey } from '../types'

/**
 * Constantes centralizadas para chaves do AsyncStorage
 * Todas tipadas para garantir type safety
 */

// Tutorial storage keys
export const TUTORIAL_STORAGE_KEYS = {
	REPLAYS: '@olhonolance:tutorial_replays' as StorageK<PERSON>,
	VIDEO_SWIPE: '@olhonolance:video-tutorial-viewed' as StorageKey,
} as const

// User data storage keys
export const USER_STORAGE_KEYS = {
	USER_DATA: '@user' as StorageKey,
} as const

// App storage keys
export const APP_STORAGE_KEYS = {
	THEME: '@theme' as StorageKey,
	LANGUAGE: '@language' as StorageKey,
} as const

// Todos as storage keys em um objeto unificado
export const STORAGE_KEYS = {
	...TUTORIAL_STORAGE_KEYS,
	...USER_STORAGE_KEYS,
	...APP_STORAGE_KEYS,
} as const

// Tipo para todas as storage keys disponíveis
export type StorageKeys = (typeof STORAGE_KEYS)[keyof typeof STORAGE_KEYS]
