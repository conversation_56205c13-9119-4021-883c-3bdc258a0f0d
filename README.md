# 📱 OlhonoLance

> Aplicação React Native/Expo para acompanhar lances esportivos em tempo real

## 📋 Principais Scripts

No arquivo `package.json`, você encontrará os seguintes scripts úteis:

- `start`: Inicia o projeto Expo
- `android`: Executa o projeto no Android
- `ios`: Executa o projeto no iOS
- `build:android:staging`: Build Android para staging
- `build:android:production`: Build Android para produção
- `build:ios:staging`: Build iOS para staging
- `build:ios:production`: Build iOS para produção
- `lint`: Executa o ESLint para verificar o código (JS e TS)
- `type-check`: Verifica tipos TypeScript sem gerar ar### 🔮 Próximas Melhorias (Opcionais)

1. 🔄 **Migrar arquivos core restantes** (58 arquivos JS) para TypeScript
2. 🔄 **Migrar componentes secundários** (VideoSwipe, etc.)
3. 🔄 **Adicionar testes TypeScript** para componentes migrados
4. 🔄 **Revisar tipos centralizados** e otimizar interfaces

### 📁 **Arquivos JS Restantes - Status de Migração**

**📈 Estatísticas Atuais:**

- **Arquivos JS restantes**: **65 arquivos** na pasta `src`
- **Migrados recentemente**: Features (6), Router (1), SplashScreen (1), Header store (1)
- **Cobertura migrada**: ~85% dos arquivos principais

#### ✅ **Features (6/6 migrados)**

- `src/features/arenas/` - arenas favoritas (4 arquivos)
- `src/features/auth/` - hooks de auth (2 arquivos)

#### ✅ **Screens Principais (2/2 migrados)**

- `src/screens/Router.tsx` - navegação principal
- `src/screens/SplashScreen/SplashScreen.tsx` - tela de loading

#### ✅ **Header (4/4 migrados)**

- `header.hook.ts` - Hook principal com tipagem completa
- `header.service.ts` - Serviços de API tipados
- `header.utils.ts` - Utilitários com interfaces TypeScript
- `header.store.ts` - Store com tipos Zustand

#### ✅ **Login (4/4 migrados)**

- `login.hook.ts`, `login.service.ts`, `login.store.ts`, `login.utils.ts`

#### 🔄 **Pendentes (58 arquivos core + 7 índices)**

- **Core screens**: ForgotPassword, SignUp, Dashboard, Profile, Replays, VideoSwipe, Arenas, Block
- **Componentes**: ArenaBox, ArenaEditModal, FilterBar, etc.
- **Índices**: Apenas re-exports (baixa prioridade)
- Cada screen tem 4-6 arquivos core (hook, service, store, utils, styles, constants)ypeScript

O projeto está migrado para TypeScript com configuração:

- **Compilação**: Babel com preset TypeScript
- **Linting**: ESLint com regras para JS e TS
- **Testes**: Jest configurado para JS, JSX, TS e TSX
- **Tipos**: Definições centralizadas em `src/types/`

## 🏗️ Arquitetura do Projeto

### 📁 Estrutura Principal (`src/`)

| Pasta           | Responsabilidade                                                                        |
| --------------- | --------------------------------------------------------------------------------------- |
| **components/** | Componentes gerais reutilizados (Atomic Design: atoms, molecules, organisms, templates) |
| **helpers/**    | Funções auxiliares como AsyncStorage, HandleMessages para feedback ao usuário           |
| **hooks/**      | Hooks globais utilizados em diversas partes da aplicação                                |
| **screens/**    | Telas da aplicação (estrutura detalhada abaixo)                                         |
| **stores/**     | Stores gerais do Zustand (user, theme, etc.)                                            |
| **utils/**      | Funções auxiliares globais (API, formatação, validação)                                 |

### 📱 Estrutura das Telas (`src/screens/<Tela>/`)

Cada tela deve seguir o padrão de nomenclatura com responsabilidades bem definidas:

| Arquivo/Pasta   | Nomenclatura               | Responsabilidade                                                   |
| --------------- | -------------------------- | ------------------------------------------------------------------ |
| **Componente**  | `<Tela>.jsx`               | Componente principal da tela (ex: `Login.jsx`)                     |
| **core/**       | Pasta para organização     | Centraliza toda a funcionalidade principal da tela                 |
| **Hook**        | `core/<tela>.hook.js`      | Lógica da tela: eventos, chamadas de API, controle de inputs       |
| **Service**     | `core/<tela>.service.js`   | Funções que integram com APIs (get/post/put/delete)                |
| **Store**       | `core/<tela>.store.js`     | Estados locais da tela: dados da API, loading, modal visível       |
| **Utils**       | `core/<tela>.utils.js`     | Funções auxiliares puras: formatação, validação, ordenação         |
| **Styles**      | `core/<tela>.styles.js`    | Estilos da tela: cores, fontes, espaçamentos, breakpoints          |
| **Constants**   | `core/<tela>.constants.js` | Valores fixos como enums, filtros, mensagens                       |
| **Stories**     | `<tela>.stories.js`        | (Opcional) Documentação de componentes usando Storybook            |
| **Types**       | `<tela>.types.js`          | (Opcional) Definição dos tipos usando `propTypes` ou similares     |
| **tests/**      | Pasta para testes          | Organização estruturada de todos os tipos de teste da tela         |
| **components/** | Pasta para componentes     | Componentes específicos desta tela (não reutilizáveis globalmente) |

#### 🗂️ **Estrutura Padrão Obrigatória**

```
src/screens/<Categoria>/<Tela>/
├── <Tela>.jsx              # Componente principal da tela (orquestrador)
├── core/                   # 📁 PASTA OBRIGATÓRIA - Funcionalidade principal
│   ├── <tela>.hook.js      # ✅ Lógica da tela (exports: hook principal)
│   ├── <tela>.service.js   # ✅ Chamadas de API (exports: funções de API)
│   ├── <tela>.store.js     # ✅ Estados locais (exports: store Zustand)
│   ├── <tela>.utils.js     # ✅ Funções auxiliares puras (exports: utilitários)
│   ├── <tela>.styles.js    # ✅ Estilos da tela (exports: função de estilos)
│   └── <tela>.constants.js # ✅ Constantes e configurações (exports: objetos config)
├── tests/                  # 📁 PASTA PARA TESTES - Organização estruturada
│   ├── unit/               # 🧪 Testes unitários (utils, store, funções puras)
│   ├── integration/        # 🔄 Testes de integração (fluxos completos)
│   ├── components/         # 🎨 Testes de componentes específicos
│   ├── performance/        # ⚡ Testes de performance e otimização
│   ├── mutation/           # 🧬 Testes de mutação (detecção de falsos positivos)
│   ├── debug/              # 🔍 Testes de debug e análise
│   ├── e2e/                # 🎯 Testes end-to-end com Detox
└── components/             # 📁 Componentes específicos da tela
    ├── <Tela>Header.jsx    # Cabeçalho específico
    ├── <Tela>Form.jsx      # Formulário específico
    └── <Tela>Footer.jsx    # Footer específico
```

---

## ✅ Boas Práticas

### 🎯 Separação de Responsabilidades

- **Não misture responsabilidades**  
  Ex: chamada de API sempre vai em `services/`, lógica de botão em `hooks/`, controle de estado em `stores/`

- **SEMPRE use callApi nos services**  
  Nunca use `axios` diretamente nos arquivos de service. Use sempre `callApi` do store `useApi` para manter consistência e gerenciamento de loading

- **Componentes pequenos e focados**  
  Evite arquivos grandes e difíceis de testar. Separe lógicas e visualizações quando possível

- **Nomes claros e semânticos**  
  Isso facilita a leitura e manutenção do código

### 🔄 Reutilização vs Isolamento

- **Reutilize o que faz sentido, isole o que for específico**  
  Se algo é usado em mais de uma tela, leve para a camada global (`src/components/`)

- **Evite lógica nos componentes principais**  
  O componente da tela deve ser o mais "limpo" possível. Lógica pertence aos hooks ou stores

- **Centralize as chamadas de API nos services**  
  Isso facilita manutenção, testes e futuras mudanças de endpoint

- **SEMPRE exporte telas no arquivo index.js**  
  Todas as telas devem ser exportadas e importadas através do arquivo `src/screens/index.js` para centralizar e facilitar a importação em rotas e outros arquivos

- **Use a pasta `core/` SEMPRE**  
  Toda funcionalidade principal da tela deve estar na pasta core, independente da complexidade

- **Use a pasta `tests/` para organização estruturada**  
  Toda tela deve ter sua pasta de testes organizada por categoria (unit, integration, components, performance, etc.)

- **Use pastas `components/` apenas quando necessário**  
  Se um componente interno é simples e usado apenas uma vez, considere mantê-lo inline ou como um arquivo separado

### 🏗️ Organização com Pasta Core

- **A pasta `core/` é OBRIGATÓRIA para todas as telas**  
  Mesmo telas simples devem ter essa organização para manter consistência

- **Componentes principais são orquestradores puros**  
  Ex: `Login.jsx` apenas importa e organiza, sem lógica de negócio

- **Hooks distribuídos nos componentes**  
  Componentes específicos acessam hooks diretamente, evitando prop drilling

- **Funções genéricas nos utils**  
  Ex: `createFormHandlers()` pode ser reutilizado em outros formulários

- **Constantes centralizadas**  
  Configurações, mensagens e rotas ficam em `constants.js`

### 📥 Organização de Imports

- **Use imports diretos dos arquivos específicos**  
  Ex: `import { myHook } from './core/myScreen.hook.js'`

- **Imports da pasta core devem seguir o padrão relativo**  
  Ex: `import { LOGIN_FORM_CONFIG } from './core/login.constants'`

- **Componentes específicos importam da pasta core usando "../"**  
  Ex: `import { useLogin } from '../core/login.hook'`

- **Evite arquivos de índice para módulos locais**  
  Imports diretos tornam o código mais legível e explícito sobre a origem de cada módulo

- **Mantenha imports organizados por contexto**  
  Agrupe imports de bibliotecas externas, depois componentes globais, depois módulos da pasta core

#### 📋 **Exemplo de Imports Corretos:**

```jsx
// <Tela>.jsx - Componente principal
import { View } from 'react-native'

// Imports da pasta core (sempre com ./core/)
import { createLoginStyles } from './core/login.styles'

// Imports de componentes específicos (sempre com ./components/)
import LoginWelcomeHeader from './components/LoginWelcomeHeader'
import LoginForm from './components/LoginForm'
import LoginFooter from './components/LoginFooter'

// Componentes específicos - imports da pasta core (sempre com ../core/)
// LoginForm.jsx
import { LOGIN_FORM_CONFIG } from '../core/login.constants'
import { createLoginStyles } from '../core/login.styles'
import { createFormHandlers } from '../core/login.utils'
import { useLogin } from '../core/login.hook'
```

#### 📦 **Arquivo Index.js - Exportação de Telas**

**⚠️ OBRIGATÓRIO**: Todas as telas devem ser exportadas no arquivo `src/screens/index.js` para centralizar as importações.

```javascript
// src/screens/index.js
export { default as Login } from './AuthRoutes/Login/Login'
export { default as SignUp } from './AuthRoutes/SignUp/SignUp'
export { default as ForgotPassword } from './AuthRoutes/ForgotPassword/ForgotPassword'

export { default as Dashboard } from './SignedRoutes/Clients/Dashboard/Dashboard'
export { default as Arenas } from './SignedRoutes/Clients/Arenas/Arenas'
// ... outras telas
```

**Uso em rotas e outros arquivos:**

```javascript
// Router.js
import { Login, SignUp, ForgotPassword, Dashboard } from '../screens'

// Ao invés de:
// import Login from '../screens/AuthRoutes/Login/Login'
// import SignUp from '../screens/AuthRoutes/SignUp/SignUp'
```

---

## 📦 Exemplo de Estrutura Completa

```
src/
├── components/
│   ├── atoms/
│   ├── molecules/
│   ├── organisms/
│   └── templates/
├── helpers/
│   ├── AsyncStorage.js
│   └── HandleMessages.js
├── hooks/
│   ├── useArenaData.js
│   └── useVideoActions.js
├── screens/
│   ├── AuthRoutes/
│   │   ├── Login/
│   │   │   ├── Login.jsx             # Componente principal
│   │   │   ├── core/                 # 📁 Funcionalidade principal
│   │   │   │   ├── login.hook.js     # Lógica da tela
│   │   │   │   ├── login.service.js  # Chamadas de API
│   │   │   │   ├── login.store.js    # Estado local
│   │   │   │   ├── login.utils.js    # Funções auxiliares
│   │   │   │   ├── login.styles.js   # Estilos
│   │   │   │   └── login.constants.js # Constantes
│   │   │   ├── tests/                # 📁 Testes estruturados
│   │   │   │   ├── unit/             # Testes unitários
│   │   │   │   │   ├── login.utils.test.js
│   │   │   │   │   └── login.store.test.js
│   │   │   │   ├── integration/      # Testes de integração
│   │   │   │   │   ├── login.flow.test.jsx
│   │   │   │   │   └── login.flow.require.test.js
│   │   │   │   ├── components/       # Testes de componentes
│   │   │   │   │   └── LoginForm.test.jsx
│   │   │   │   ├── performance/      # Testes de performance
│   │   │   │   │   └── login.performance.test.js
│   │   │   │   ├── mutation/         # Testes de mutação
│   │   │   │   │   └── mutation.test.js
│   │   │   │   ├── debug/            # Testes de debug
│   │   │   │   │   └── email-debug.test.js
│   │   │   │   ├── e2e/              # Testes end-to-end
│   │   │   │   │   └── login.e2e.js
│   │   │   │   └── README.md         # Doc dos testes
│   │   │   └── components/           # Componentes específicos
│   │   │       ├── LoginWelcomeHeader.jsx
│   │   │       ├── LoginForm.jsx
│   │   │       └── LoginFooter.jsx
│   │   └── SignUp/
│   │       ├── SignUp.jsx
│   │       ├── core/
│   │       │   ├── signup.hook.js
│   │       │   ├── signup.service.js
│   │       │   ├── signup.store.js
│   │       │   ├── signup.utils.js
│   │       │   ├── signup.styles.js
│   │       │   └── signup.constants.js
│   │       ├── tests/                # 📁 Testes estruturados
│   │       │   ├── unit/
│   │       │   ├── integration/
│   │       │   ├── components/
│   │       │   ├── performance/
│   │       │   ├── mutation/
│   │       │   ├── debug/
│   │       │   ├── e2e/
│   │       │   └── README.md
│   │       └── components/
│   │           ├── SignUpForm.jsx
│   │           └── SignUpFooter.jsx
│   └── SignedRoutes/
│       ├── Home/
│       │   ├── Home.jsx
│       │   ├── core/
│       │   │   ├── home.hook.js
│       │   │   ├── home.store.js
│       │   │   ├── home.service.js
│       │   │   ├── home.styles.js
│       │   │   └── home.constants.js
│       │   ├── tests/                # 📁 Testes estruturados
│       │   │   ├── unit/
│       │   │   ├── integration/
│       │   │   ├── components/
│       │   │   ├── performance/
│       │   │   ├── mutation/
│       │   │   ├── debug/
│       │   │   ├── e2e/
│       │   │   └── README.md
│       │   └── components/
│       │       ├── HomeHeader.jsx
│       │       └── HomeContent.jsx
│       └── Arena/
│           ├── Arena.jsx
│           ├── core/
│           │   ├── arena.hook.js
│           │   ├── arena.service.js
│           │   ├── arena.store.js
│           │   ├── arena.styles.js
│           │   └── arena.constants.js
│           ├── tests/                # 📁 Testes estruturados
│           │   ├── unit/
│           │   ├── integration/
│           │   ├── components/
│           │   ├── performance/
│           │   ├── mutation/
│           │   ├── debug/
│           │   ├── e2e/
│           │   └── README.md
│           └── components/
│               ├── ArenaLive.jsx
│               └── ArenaReplays.jsx
├── stores/
│   └── api.store.js
└── utils/
    └── api.js
```

---

## 🎯 Padrões de Implementação

### 📋 **Store Pattern (Zustand)**

```javascript
// core/<tela>.store.js
import { create } from 'zustand'

const initialState = {
	campo1: '',
	campo2: false,
}

export const use<Tela>Store = create((set) => ({
	...initialState,

	// Funções genéricas (OBRIGATÓRIO)
	updateField: (field, value) => set({ [field]: value }),
	toggleField: (field) => set((state) => ({ [field]: !state[field] })),

	// Resetar estado
	resetForm: () => set(initialState),

	// Funções específicas quando necessário
	setCustomData: (data) => set({ ...data }),
}))
```

### 🔧 **Hook Pattern**

```javascript
// core/<tela>.hook.js
export const use<Tela> = () => {
	const { campo1, campo2, updateField, toggleField } = use<Tela>Store()

	const handleSpecificAction = async () => {
		// Lógica específica
	}

	return {
		// States
		campo1,
		campo2,

		// Generic handlers
		updateField,
		toggleField,

		// Specific actions
		handleSpecificAction,
	}
}
```

### 🛠️ **Utils Pattern**

```javascript
// core/<tela>.utils.js
/**
 * Cria handlers genéricos para formulários
 */
export const createFormHandlers = (updateField, toggleField) => ({
	handleTextChange: (field) => (value) => updateField(field, value),
	handleToggle: (field) => () => toggleField(field),
})

/**
 * Funções de validação puras
 */
export const validate<Tela>Form = (data) => {
	// Lógica de validação
}
```

### 📝 **Constants Pattern**

```javascript
// core/<tela>.constants.js
export const <TELA>_FORM_CONFIG = {
	campo1: { label: 'Label 1', placeholder: 'placeholder...' },
	campo2: { label: 'Label 2' },
}

export const <TELA>_VALIDATION_MESSAGES = {
	REQUIRED: 'Campo obrigatório',
	INVALID: 'Formato inválido',
}

export const <TELA>_ROUTES = {
	SUCCESS: 'SuccessScreen',
	ERROR: 'ErrorScreen',
}
```

---

## 📝 Padrão de Nomenclatura

### Arquivos Gerais

- Use PascalCase para componentes: `MyComponent.tsx` (TypeScript) ou `MyComponent.jsx` (JavaScript)
- Use camelCase para utilitários: `myUtility.ts` ou `myUtility.js`
- **Preferência para TypeScript**: Novos componentes devem ser criados em TypeScript (.tsx/.ts)

### Arquivos Específicos de Tela

- **Componente principal**: `<Tela>.tsx` (PascalCase) - migrado para TypeScript
- **Arquivos da pasta core**: `<tela>.<funcao>.js` (sempre minúsculas) - mantidos em JS por enquanto
- Exemplos:
    - `core/home.store.js`
    - `core/arena.hook.js`
    - `core/live.service.js`
    - `core/replay.utils.js`
    - `core/login.constants.js`
    - `core/signup.styles.js`

### Componentes Específicos da Tela

- **Nomenclatura**: `<Tela><Funcionalidade>.tsx` (PascalCase) - **Migrados para TypeScript**
- **❌ Evitar**: Não usar `index.tsx` como nome do componente principal
- **✅ Correto**: Usar o nome real do componente
- **✅ Migração Completa**: Todos os componentes principais já estão em TypeScript
- Exemplos:
    - `components/LoginForm.tsx`
    - `components/LoginWelcomeHeader.tsx`
    - `components/LoginFooter.tsx`
    - `components/ArenaLive.tsx`
    - `components/ArenaReplays.tsx`

### Funcionalidades Internas

- Para sub-funcionalidades: `<tela>.<subfuncionalidade>.<funcao>.js`
- Exemplos:
    - `core/arena.live.store.js`
    - `core/home.highlights.hook.js`
    - `core/replay.video.utils.js`

---

## 🏷️ TypeScript

### Status da Migração

**✅ Componentes Migrados para TypeScript:**

#### **Atoms (100% migrados)**

- `Button/Button.tsx` - Componente de botão com tipos para variants e props
- `Icon/Icon.tsx` - Componente de ícone com tipos para bibliotecas e tamanhos
- `Text/Text.tsx` - Componente de texto com tipos para tamanhos e estilos
- `Dropdown/Dropdown.tsx` - Componente dropdown com tipos para dados e callbacks

#### **Molecules (100% migrados)**

- `Box/Box.tsx` - Componente de caixa com tipos para interações
- `Check/Check.tsx` - Componente de checkbox com tipos para estado
- `Item/Item.tsx` - Componente de item de lista com tipos
- `TextInput/TextInput.tsx` - Componente de input com tipos para validação
- `Video/Video.tsx` - Componente de vídeo com tipos para player

#### **Organisms (100% migrados)**

- `Header/Header.tsx` - Cabeçalho principal com tipos para props de navegação
    - `HeaderActions.tsx` - Ações do cabeçalho
    - `HeaderGreeting.tsx` - Saudação do usuário
    - `SearchInput.tsx` - Campo de busca
    - `SearchResults.tsx` - Resultados da busca
- `Modal/Modal.tsx` - Modal reutilizável com tipos para conteúdo
- `ScrollHorizontal/ScrollHorizontal.tsx` - Scroll horizontal com tipos para items
- `ToastMessages/ToastMessages.tsx` - Mensagens toast com tipos para variants

#### **Screens Migradas**

- `AuthRoutes/Login/Login.tsx` - Tela de login
- `AuthRoutes/SignUp/SignUp.tsx` - Tela de cadastro
- `AuthRoutes/ForgotPassword/ForgotPassword.tsx` - Tela de recuperação de senha
- `SignedRoutes/Profile/Profile.tsx` - Tela de perfil
    - `ProfileHeader.tsx` - Cabeçalho do perfil
    - `ProfileForm.tsx` - Formulário do perfil
    - `ProfileFooter.tsx` - Footer do perfil
- `SignedRoutes/Replays/Replays.tsx` - Tela de replays
- `SignedRoutes/Clients/Dashboard/Dashboard.tsx` - Dashboard principal

#### **Stores Migradas**

- `api.store.ts` - Store de API com tipos para requisições
- `theme.store.ts` - Store de tema com tipos para cores e espacamentos
- `user.store.ts` - Store de usuário com tipos para autenticação

### Convenções TypeScript

#### **Interfaces e Tipos**

```typescript
// Centralizar tipos em src/types/index.ts
export interface ButtonProps {
	type?: 'text' | 'outlined' | 'contained'
	size?: 'small' | 'medium' | 'large'
	loading?: boolean
	onPress?: () => void
	children?: React.ReactNode
}

// Tipos para tema
export interface Theme {
	type: 'dark' | 'light'
	colors: ThemeColors
	fontSize: FontSizes
	spacing: ThemeSpacing
}
```

#### **Componentes Funcionais**

```typescript
// Componente com tipos explícitos
import React from 'react'
import type { ButtonProps } from '../../types'

const Button: React.FC<ButtonProps> = ({ type = 'contained', size = 'medium', children, ...props }) => {
	// implementação
}

export default Button
```

#### **Hooks Customizados**

```typescript
// Hook com tipos de retorno
interface UseLoginReturn {
	loading: boolean
	handleLogin: (credentials: LoginCredentials) => Promise<void>
	error?: string
}

export const useLogin = (): UseLoginReturn => {
	// implementação
}
```

### Regras de Migração

1. **Componentes Novos**: Sempre criar em TypeScript (.tsx/.ts)
2. **Migração Gradual**: Migrar componentes existentes por ordem de prioridade
3. **Tipos Centralizados**: Manter tipos em `src/types/index.ts`
4. **Props Tipadas**: Sempre tipar props de componentes
5. **Imports Explícitos**: Usar extensões nos imports TypeScript
6. **Nomenclatura**: Manter PascalCase para componentes, evitar `index.tsx`

### ✅ Progresso da Migração

**Status Atual: ~95% dos componentes principais migrados**

📊 **Estatísticas da Migração:**

- ✅ **54 arquivos TypeScript** (.ts/.tsx) criados
- 🔄 **4 arquivos JSX restantes** (componentes específicos não críticos)
- ✅ **36 componentes TSX** migrados com tipos completos
- ✅ **18 arquivos TS** (stores, tipos, configurações)

**Componentes por Categoria:**

- ✅ **Atoms**: 100% migrados (Button, Icon, Text, Dropdown)
- ✅ **Molecules**: 100% migrados (Box, Check, Item, TextInput, Video)
- ✅ **Organisms**: 100% migrados (Header, Modal, ScrollHorizontal, ToastMessages)
- ✅ **Screens Principais**: ~95% migradas (Login, SignUp, Profile, Replays, Dashboard)
- ✅ **Stores**: 100% migradas (api.store.ts, theme.store.ts, user.store.ts)
- 🔄 **Componentes Específicos**: 4 arquivos JSX restantes
- 🔄 **Arquivos Core**: Mantidos em JS por design (hooks, services, utils, styles)

### 🎯 Status: Migração TypeScript Concluída! 🎉

**📈 Estatísticas Finais:**

- ✅ **40 arquivos .tsx** - 100% dos componentes/screens migrados
- ✅ **18 arquivos .ts** - Tipos, stores, utils e hooks
- ✅ **0 arquivos .jsx restantes** - Migração finalizada!
- ✅ **Cobertura TypeScript**: 100% dos componentes principais

**Componentes Migrados por Categoria:**

- ✅ **Atoms**: 100% migrados (6/6 componentes)
- ✅ **Molecules**: 100% migrados (3/3 componentes)
- ✅ **Organisms**: 100% migrados (3/3 componentes)
- ✅ **Screens Principais**: 100% migradas (6/6 telas)
- ✅ **Componentes Específicos**: 100% migrados (4/4 componentes)
- ✅ **Stores**: 100% migradas (api.store.ts, theme.store.ts, user.store.ts)
- 📝 **Arquivos Core**: Mantidos em JS por design (hooks, services, utils, styles)

### 🏆 Objetivos Concluídos

1. ✅ **Finalizar migração dos componentes principais** - CONCLUÍDO
2. ✅ **Migrar componentes específicos restantes** - CONCLUÍDO (0 JSX restantes)
3. ✅ **Configurar Jest para TypeScript** - CONCLUÍDO
4. ✅ **Atualizar documentação** - CONCLUÍDO
5. ✅ **Aplicar convenções de nomenclatura** - CONCLUÍDO (sem index.js como main)
6. ✅ **Validação TypeScript** - CONCLUÍDO (tsc --noEmit limpo)

### 🔮 Próximas Melhorias (Opcionais)

1. 🔄 **Migrar arquivos core restantes** (31 arquivos JS) para TypeScript
2. 🔄 **Migrar telas secundárias** (Arenas, Block, VideoSwipe)
3. 🔄 **Adicionar testes TypeScript** para componentes migrados
4. 🔄 **Revisar tipos centralizados** e otimizar interfaces

### � **Arquivos Core Migrados**

#### ✅ **Header (3/3 migrados)**

- `header.hook.ts` - Hook principal com tipagem completa
- `header.service.ts` - Serviços de API tipados
- `header.utils.ts` - Utilitários com interfaces TypeScript

#### 🔄 **Pendentes (31 arquivos core)**

- Screens: Login, SignUp, ForgotPassword, Replays, Dashboard, Profile, etc.
- Cada screen tem 4-6 arquivos core (hook, service, store, utils, styles, constants)### 🛠️ Configuração TypeScript Completa

- ✅ **tsconfig.json**: Configuração strict com allowJs
- ✅ **babel.config.js**: Preset TypeScript adicionado
- ✅ **eslintrc.js**: Regras para JS e TS configuradas (conflitos resolvidos)
- ✅ **jest.config.js**: Suporte para .ts/.tsx adicionado
- ✅ **Tipos centralizados**: Interfaces em `src/types/index.ts`
- ✅ **.prettierrc**: Configuração de formatação consistente
- ✅ **.vscode/settings.json**: Configuração VS Code para TypeScript
- ✅ **Scripts npm**: `format`, `lint:fix`, `type-check` adicionados

### 🔧 Problema Resolvido: Conflitos de Formatação

**Problema Identificado:**
Conflito entre ESLint e Prettier/VS Code causando loop de formatação nos arquivos TypeScript.

**Solução Implementada:**

1. **Configuração Prettier** (`.prettierrc`): Regras consistentes com tabs
2. **ESLint ajustado**: Desabilitadas regras de indentação conflitantes
3. **VS Code configurado**: Prettier como formatador padrão, ESLint para lint only
4. **Scripts adicionados**: `format`, `lint:fix` para manutenção do código

**Resultado:**
Agora o VS Code formata corretamente ao salvar sem conflitos entre ferramentas.

---

## 🚀 Uso da API

**⚠️ OBRIGATÓRIO**: Sempre utilize a função `callApi` do store `useApi` nos arquivos de service. Nunca use `axios` diretamente nos services.

```javascript
// core/<tela>.service.js
import { useApi } from '../../../stores/api.store'

const { callApi } = useApi.getState()

/**
 * Busca dados da tela
 */
export const fetch<Tela>Data = async () => callApi({
	section: '<tela>',
	method: 'GET',
	url: '/<endpoint>',
})

/**
 * Envia dados da tela
 */
export const save<Tela>Data = async (data) => callApi({
	method: 'POST',
	section: '<tela>Save',
	url: '/<endpoint>',
	body: data,
})
```

### Hook de consumo:

```javascript
// core/<tela>.hook.js
import { save<Tela>Data } from './<tela>.service'

export const use<Tela> = () => {
	const handleSave = async (formData) => {
		const result = await save<Tela>Data(formData)
		if (result) {
			// Sucesso
		}
	}

	return { handleSave }
}
```

---

## 🧪 Testes

### 📋 **Organização de Testes por Tela**

- **Testes devem ser organizados por tela** em uma pasta `tests/` estruturada por categoria
- **Para testes globais** use a pasta `__tests__/` na raiz ou dentro de helpers, stores globais, etc.
- **Cada tela mantém seus próprios testes** seguindo a estrutura organizacional padrão

### 🗂️ **Estrutura Padrão de Testes**

```
src/screens/<Categoria>/<Tela>/tests/
├── unit/                           # 🧪 Testes Unitários
│   ├── <tela>.utils.test.js        # Funções puras, validação, formatação
│   └── <tela>.store.test.js        # Store Zustand, estados, actions
├── integration/                    # 🔄 Testes de Integração
│   ├── <tela>.flow.test.jsx        # Fluxo completo da tela
│   └── <tela>.api.test.js          # Integração com APIs
├── components/                     # 🎨 Testes de Componente
│   ├── <Tela>Form.test.jsx         # Componentes específicos
│   └── <Tela>Header.test.jsx       # Interações e renderização
├── performance/                    # ⚡ Testes de Performance
│   └── <tela>.performance.test.js  # Tempo de resposta, uso de memória
├── mutation/                       # 🧬 Testes de Mutação
│   └── mutation.test.js            # Detecção de falsos positivos
├── debug/                          # 🔍 Testes de Debug
│   └── <funcionalidade>-debug.test.js # Análise e depuração
├── e2e/                           # 🎯 Testes End-to-End
│   └── <tela>.e2e.js              # Testes completos com Detox
└── README.md                      # Documentação dos testes
```

### 🎯 **Tipos de Teste por Categoria**

| Categoria       | Foco                          | Ferramentas            | Exemplos                      |
| --------------- | ----------------------------- | ---------------------- | ----------------------------- |
| **Unit**        | Funções puras, lógica isolada | Jest                   | Validação, formatação, utils  |
| **Integration** | Fluxos e integrações          | Jest + RTL             | Login completo, chamadas API  |
| **Components**  | Renderização e interação      | Jest + RTL             | Componentes específicos       |
| **Performance** | Otimização e recursos         | Jest + Performance API | Tempo, memória, rendering     |
| **Mutation**    | Qualidade dos testes          | Jest + Stryker         | Detecção de falsos positivos  |
| **Debug**       | Análise e depuração           | Jest + Console         | Regex, logs, troubleshooting  |
| **E2E**         | Fluxo completo do usuário     | Detox                  | Navegação, inputs, validações |

### 📝 **Scripts de Teste Recomendados**

```json
{
	"scripts": {
		"test": "jest",
		"test:watch": "jest --watch",
		"test:coverage": "jest --coverage",
		"test:unit": "jest src/**/tests/unit",
		"test:integration": "jest src/**/tests/integration",
		"test:component": "jest src/**/tests/components",
		"test:performance": "jest src/**/tests/performance",
		"test:mutation": "jest src/**/tests/mutation",
		"test:debug": "jest src/**/tests/debug",
		"test:e2e": "detox test",
		"test:login": "jest src/screens/AuthRoutes/Login/tests",
		"test:signup": "jest src/screens/AuthRoutes/SignUp/tests",
		"test:home": "jest src/screens/SignedRoutes/Home/tests"
	}
}
```

### ✅ **Boas Práticas para Testes**

- **Organize por responsabilidade**: Cada tipo de teste tem sua pasta específica
- **Documente a estrutura**: Cada pasta de testes deve ter seu README.md
- **Use nomenclatura consistente**: `<tela>.<tipo>.test.js` ou `<Componente>.test.jsx`
- **Mantenha testes próximos ao código**: Testes da tela ficam na pasta da tela
- **Crie scripts específicos**: Facilite a execução de categorias de teste
- **Teste lógicas complexas**: Foque nos hooks, services e utils
- **Teste integrações críticas**: APIs, navegação, fluxos principais
- **Use ferramentas apropriadas**: Jest + RTL para componentes, Detox para E2E

### 🏗️ **Exemplos de Organização**

- `src/screens/AuthRoutes/Login/tests/` - todos os testes da tela Login
- `src/components/atoms/Button/Button.test.js` - teste do componente Button
- `src/helpers/__tests__/` - testes de helpers globais (exceção à regra)
- `src/stores/__tests__/` - testes de stores globais (exceção à regra)

### 🔄 **Integração com CI/CD**

- Configure execução automática para diferentes categorias
- Use coverage reports para monitorar qualidade
- Execute testes E2E em ambientes de staging
- Integre mutation testing para validar qualidade dos testes

---

## 📚 Storybook

Para documentar componentes:

1. Execute: `yarn storybook`
2. Crie stories usando a nomenclatura `<tela>.stories.js` dentro da pasta da tela
3. Para componentes globais, use `<Componente>.stories.js` dentro da pasta do componente
4. Documente props e estados dos componentes
