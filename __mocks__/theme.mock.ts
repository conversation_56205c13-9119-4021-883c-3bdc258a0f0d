// Global theme mock for all React Native component tests
// This provides consistent theme values across all test files

export const mockTheme = {
	type: 'dark',
	colors: {
		primary: '#59D966',
		primary50: '#59D96650',
		secondary: '#252525',
		secondary50: '#25252550',
		tertiary: '#4E4E4E',
		tertiary50: '#4E4E4E50',
		background: '#1F1F1F',
		text: '#F2F2F2',
		white: '#FFFFFF',
		black: '#000000',
		blue: '#22C7DD',
		textLight: '#9E9E9E',
		red: '#DA1C1C',
		yellow: '#FFD700',
		base100: '#0F1110',
		base200: '#474A48',
		base300: '#1F2120',
		base400: '#2D2F2E',
		error: '#cc5356',
	},
	fontSize: {
		extraSmall: 8,
		small: 10,
		medium: 14,
		large: 18,
		extraLarge: 20,
	},
	spacing: {
		s1: 8,
		s2: 16,
		s3: 24,
		s4: 32,
		s5: 40,
		s6: 48,
		s7: 56,
		s8: 64,
		s9: 72,
		s10: 80,
	},
}

// Mock functions for theme store
export const mockUseTheme = jest.fn(() => ({
	theme: mockTheme,
}))

export const mockThemeStore = {
	useTheme: mockUseTheme,
}
