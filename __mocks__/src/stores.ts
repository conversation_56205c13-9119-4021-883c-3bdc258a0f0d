// Mock for src/stores module
// Provides consistent store mocks for all React Native component tests

import { mockTheme } from '../theme.mock'

const mockStores = {
	useTheme: jest.fn(() => ({
		theme: mockTheme,
		toggleTheme: jest.fn(),
		setTheme: jest.fn(),
	})),

	useUser: jest.fn(() => ({
		user: null,
		setUser: jest.fn(),
		clearUser: jest.fn(),
	})),

	useUserComputed: jest.fn(() => ({
		isAuthenticated: false,
		userName: null,
	})),

	useApi: jest.fn(() => ({
		loading: false,
		sections: {},
		setLoading: jest.fn(),
		setSectionLoading: jest.fn(),
	})),

	useFavoriteArenas: jest.fn(() => ({
		favorites: [],
		addToFavorites: jest.fn(),
		removeFromFavorites: jest.fn(),
		isFavorite: jest.fn(() => false),
	})),
}

export default mockStores
