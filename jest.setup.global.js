// Global test setup for React Native component tests
// This file is automatically loaded by Je<PERSON> and sets up all necessary mocks

// Mock the stores globally
jest.mock('../src/stores')

// Mock React Native components that might cause issues in tests
jest.mock('react-native', () => {
	const RN = jest.requireActual('react-native')

	// Mock problematic components
	RN.NativeModules = {
		...RN.NativeModules,
		StatusBarManager: {
			HEIGHT: 20,
			getHeight: jest.fn((cb) => cb(20)),
		},
	}

	return RN
})

// Mock Expo components globally
jest.mock('expo-linear-gradient', () => ({
	LinearGradient: ({ children, ...props }) => children,
}))

jest.mock('react-native-paper', () => ({
	ActivityIndicator: ({ testID, ...props }) => ({
		testID: testID || 'ActivityIndicator',
		...props,
	}),
}))

jest.mock('@expo/vector-icons', () => ({
	FontAwesome: ({ testID, ...props }) => ({
		testID: testID || 'FontAwesome',
		...props,
	}),
	FontAwesome5: ({ testID, ...props }) => ({
		testID: testID || 'FontAwesome5',
		...props,
	}),
	FontAwesome6: ({ testID, ...props }) => ({
		testID: testID || 'FontAwesome6',
		...props,
	}),
	MaterialIcons: ({ testID, ...props }) => ({
		testID: testID || 'MaterialIcons',
		...props,
	}),
	Ionicons: ({ testID, ...props }) => ({
		testID: testID || 'Ionicons',
		...props,
	}),
	AntDesign: ({ testID, ...props }) => ({
		testID: testID || 'AntDesign',
		...props,
	}),
	Entypo: ({ testID, ...props }) => ({
		testID: testID || 'Entypo',
		...props,
	}),
	EvilIcons: ({ testID, ...props }) => ({
		testID: testID || 'EvilIcons',
		...props,
	}),
	Feather: ({ testID, ...props }) => ({
		testID: testID || 'Feather',
		...props,
	}),
	Fontisto: ({ testID, ...props }) => ({
		testID: testID || 'Fontisto',
		...props,
	}),
	MaterialCommunityIcons: ({ testID, ...props }) => ({
		testID: testID || 'MaterialCommunityIcons',
		...props,
	}),
	Octicons: ({ testID, ...props }) => ({
		testID: testID || 'Octicons',
		...props,
	}),
	SimpleLineIcons: ({ testID, ...props }) => ({
		testID: testID || 'SimpleLineIcons',
		...props,
	}),
}))

// Global test utilities
global.mockTheme = require('./__mocks__/theme.mock').mockTheme
