{"name": "olhonolance", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "build:android:staging": "eas build -p android --profile staging --local", "build:android:production": "eas build -p android --profile production --auto-submit", "build:ios:staging": "eas build -p ios --profile staging --local", "build:ios:production": "eas build -p ios --profile production --auto-submit", "submit:android": "eas submit -p android", "submit:ios": "eas submit -p ios", "update:staging": "eas update --branch staging", "update:production": "eas update --branch production", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=\"(src/.*/tests/.*unit|src/stores/tests|src/screens/.*/tests/.*validations)\"", "test:component": "jest --testPathPattern=\"(src/components/tests|src/components/.*/tests)\"", "test:integration": "jest --testPathPattern=\"(src/.*/tests/.*integration|src/screens/.*/tests/.*integration)\"", "test:performance": "jest --testPathPattern=\"src/tests/performance\"", "test:mutation": "jest --testPathPattern=\"src/tests/regression\"", "test:debug": "jest --testPathPattern=\"src/tests/debug\"", "test:e2e": "detox test --configuration ios", "test:e2e:build": "detox build --configuration ios", "test:e2e:ios": "detox test --configuration ios", "test:e2e:ios:build": "detox build --configuration ios", "test:e2e:android": "detox test --configuration android", "test:e2e:android:build": "detox build --configuration android", "test:e2e:clean": "detox clean-framework-cache && detox build", "test:e2e:help": "echo 'E2E Commands: test:e2e (iOS default), test:e2e:ios, test:e2e:android, test:e2e:*:build, test:e2e:clean'", "project-tree": "fd --type f --hidden --exclude .git | tree --fromfile --dirsfirst > project-tree.txt"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/native": "^6.1.6", "@react-navigation/native-stack": "^6.9.26", "@react-navigation/stack": "^7.1.1", "axios": "1.6.8", "base64-arraybuffer": "^1.0.2", "expo": "^53.0.20", "expo-av": "~15.1.7", "expo-dev-client": "~5.2.4", "expo-file-system": "~18.1.11", "expo-font": "~13.3.2", "expo-linear-gradient": "~14.1.5", "expo-media-library": "~17.1.7", "expo-navigation-bar": "~4.2.7", "expo-screen-orientation": "~8.1.7", "expo-sharing": "~13.1.5", "expo-store-review": "~8.1.5", "expo-updates": "~0.28.17", "lottie-react-native": "7.2.2", "moment": "^2.30.1", "moti": "^0.30.0", "prop-types": "^15.8.1", "react": "19.0.0", "react-native": "0.79.5", "react-native-awesome-slider": "^2.9.0", "react-native-copilot": "^3.3.2", "react-native-element-dropdown": "^2.12.4", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "~2.24.0", "react-native-masked-text": "^1.13.0", "react-native-modal": "~14.0.0-rc.1", "react-native-paper": "~5.12.3", "react-native-popup-confirm-toast": "^2.3.9", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-toast-message": "^2.2.0", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/preset-typescript": "^7.27.1", "@eslint/js": "^9.2.0", "@jest/globals": "^30.0.5", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.9.0", "@types/jest": "^30.0.0", "@types/node": "^24.2.0", "@types/react": "^19.1.9", "@types/react-native": "^0.72.8", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "babel-eslint": "^10.1.0", "detox": "^20.40.2", "eslint": "^7.32.0 || ^8.2.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.2", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "eslint-plugin-react-native": "^4.1.0", "jest": "~29.7.0", "jest-environment-jsdom": "^30.0.5", "prettier": "^3.2.5", "react-test-renderer": "^19.0.0", "typescript": "^5.9.2"}, "private": true}