import { TUTORIAL_STORAGE_KEYS } from '../src/constants/storage'
import { TUTORIAL_STORAGE_KEY } from '../src/screens/SignedRoutes/Replays/core/replays.constants'

describe('Storage Constants', () => {
	test('TUTORIAL_STORAGE_KEY should be defined and have correct value', () => {
		expect(TUTORIAL_STORAGE_KEY).toBeDefined()
		expect(typeof TUTORIAL_STORAGE_KEY).toBe('string')
		expect(TUTORIAL_STORAGE_KEY).toBe(TUTORIAL_STORAGE_KEYS.REPLAYS)
		expect(TUTORIAL_STORAGE_KEY).toBe('@olhonolance:tutorial_replays')
	})

	test('TUTORIAL_STORAGE_KEY should not be undefined or empty', () => {
		expect(TUTORIAL_STORAGE_KEY).not.toBeUndefined()
		expect(TUTORIAL_STORAGE_KEY).not.toBeNull()
		expect(TUTORIAL_STORAGE_KEY).not.toBe('')
		expect(TUTORIAL_STORAGE_KEY.length).toBeGreaterThan(0)
	})
})
