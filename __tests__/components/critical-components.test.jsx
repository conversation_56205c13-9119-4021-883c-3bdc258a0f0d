import { render } from '@testing-library/react-native'

// Import real components - theme is mocked globally
import Text from '../../src/components/atoms/Text'

// Import theme for test expectations
const { mockTheme } = require('../../__mocks__/theme.mock')

describe('Critical Components', () => {
	describe('Text Component', () => {
		it('should render text with children', () => {
			const { getByText } = render(<Text size="medium">Hello World</Text>)
			expect(getByText('Hello World')).toBeTruthy()
		})

		it('should apply correct font size from real theme', () => {
			const { getByText } = render(<Text size="large">Large Text</Text>)
			const textElement = getByText('Large Text')
			expect(textElement).toBeTruthy()
			expect(textElement.props.style.fontSize).toBe(mockTheme.fontSize.large)
		})

		it('should apply medium size by default when no size provided', () => {
			const { getByText } = render(<Text>Default Text</Text>)
			const textElement = getByText('Default Text')
			expect(textElement).toBeTruthy()
			// When no size is provided, should use medium fontSize from theme
			expect(textElement.props.style.fontSize).toBe(mockTheme.fontSize.medium)
		})

		it('should apply custom styles correctly', () => {
			const customStyle = { color: 'red', fontSize: 25 }
			const { getByText } = render(<Text style={customStyle}>Styled Text</Text>)
			const textElement = getByText('Styled Text')
			expect(textElement).toBeTruthy()
		})

		it('should use real theme text color', () => {
			const { getByText } = render(<Text size="small">Colored Text</Text>)
			const textElement = getByText('Colored Text')
			expect(textElement.props.style.color).toBe(mockTheme.colors.text)
		})

		it('should handle all font sizes from real theme', () => {
			const sizes = ['extraSmall', 'small', 'medium', 'large', 'extraLarge']
			const expectedSizes = [
				mockTheme.fontSize.extraSmall,
				mockTheme.fontSize.small,
				mockTheme.fontSize.medium,
				mockTheme.fontSize.large,
				mockTheme.fontSize.extraLarge,
			]

			sizes.forEach((size, index) => {
				const { getByText } = render(<Text size={size}>{size} Text</Text>)
				const textElement = getByText(`${size} Text`)
				expect(textElement.props.style.fontSize).toBe(expectedSizes[index])
			})
		})

		it('should use Montserrat font family', () => {
			const { getByText } = render(<Text>Font Test</Text>)
			const textElement = getByText('Font Test')
			expect(textElement.props.style.fontFamily).toBe('Montserrat')
		})
	})
})
