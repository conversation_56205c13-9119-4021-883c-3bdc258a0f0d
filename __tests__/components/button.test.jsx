import { fireEvent, render } from '@testing-library/react-native'

// Import real components - all dependencies mocked globally
import Button from '../../src/components/atoms/Button/Button'
import Text from '../../src/components/atoms/Text'

// Import theme for test expectations
const { mockTheme } = require('../../__mocks__/theme.mock')

// Mock expo dependencies for Button component
jest.mock('expo-linear-gradient', () => ({
	LinearGradient: ({ children, ...props }) => children,
}))

jest.mock('react-native-paper', () => ({
	ActivityIndicator: 'ActivityIndicator',
}))

// Mock @expo/vector-icons for Icon component
jest.mock('@expo/vector-icons', () => ({
	AntDesign: 'AntDesign',
	Entypo: 'Entypo',
	EvilIcons: 'EvilIcons',
	Feather: 'Feather',
	FontAwesome: 'FontAwesome',
	FontAwesome5: 'FontAwesome5',
	FontAwesome6: 'FontAwesome6',
	Fontisto: 'Fontisto',
	Ionicons: 'Ionicons',
	MaterialCommunityIcons: 'MaterialCommunityIcons',
	MaterialIcons: 'MaterialIcons',
	Octicons: 'Octicons',
}))

// Mock expo-font
jest.mock('expo-font', () => ({
	loadAsync: jest.fn(),
	isLoaded: jest.fn(() => true),
}))

describe('Button Component', () => {
	it('should render button with label', () => {
		const { getByText } = render(<Button label="Test Button" />)
		expect(getByText('Test Button')).toBeTruthy()
	})

	it('should handle onPress events', () => {
		const onPressMock = jest.fn()
		const { getByRole } = render(<Button label="Press Me" onPress={onPressMock} />)

		fireEvent.press(getByRole('button'))
		expect(onPressMock).toHaveBeenCalledTimes(1)
	})

	it('should use theme colors from global mock', () => {
		const { getByRole } = render(<Button label="Themed Button" />)

		const buttonElement = getByRole('button')
		expect(buttonElement).toBeTruthy()
		// Theme is consistently available from global mock
		expect(mockTheme.colors.primary).toBe('#59D966')
		expect(mockTheme.colors.secondary).toBe('#252525')
	})

	it('should show loading state', () => {
		const { getByTestId } = render(<Button label="Loading" loading={true} testID="loading-button" />)
		// Simply verify the button is rendered when loading is true
		expect(getByTestId('loading-button')).toBeTruthy()
	})

	it('should apply theme spacing from global mock', () => {
		const { getByRole } = render(<Button label="Spaced Button" />)

		const buttonElement = getByRole('button')
		expect(buttonElement).toBeTruthy()
		// Spacing values are consistent from global mock
		expect(mockTheme.spacing.s1).toBe(8)
		expect(mockTheme.spacing.s2).toBe(16)
	})

	// Additional tests for better coverage
	it('should render button with icon', () => {
		const { getByTestId } = render(
			<Button
				label="Icon Button"
				icon={{
					library: 'AntDesign',
					name: 'star',
					size: 'medium',
					color: '#fff',
				}}
				testID="icon-button"
			/>
		)
		expect(getByTestId('icon-button')).toBeTruthy()
	})

	it('should render text type button', () => {
		const { getByRole } = render(<Button label="Text Button" type="text" />)
		expect(getByRole('button')).toBeTruthy()
	})

	it('should render outlined type button', () => {
		const { getByRole } = render(<Button label="Outlined Button" type="outlined" />)
		expect(getByRole('button')).toBeTruthy()
	})

	it('should render contained type button with gradient', () => {
		const { getByRole } = render(<Button label="Contained Button" type="contained" />)
		expect(getByRole('button')).toBeTruthy()
	})

	it('should render button with custom colors', () => {
		const { getByRole } = render(<Button label="Custom Colors" colors={['#ff0000', '#00ff00']} />)
		expect(getByRole('button')).toBeTruthy()
	})

	it('should render button with children instead of label', () => {
		const { getByText } = render(
			<Button>
				<Text>Custom Child</Text>
			</Button>
		)
		expect(getByText('Custom Child')).toBeTruthy()
	})

	it('should render button without label and without icon', () => {
		const onPressMock = jest.fn()
		const { getByRole } = render(<Button onPress={onPressMock} testID="no-label-button" />)
		expect(getByRole('button')).toBeTruthy()
	})

	it('should use default accessibility label when no label provided', () => {
		const onPressMock = jest.fn()
		const { getByRole } = render(<Button onPress={onPressMock} />)
		const button = getByRole('button')
		expect(button).toBeTruthy()
		// Button should have default accessibility label
		expect(button.props.accessibilityLabel).toBe('Button')
	})

	it('should use custom accessibility label when provided', () => {
		const onPressMock = jest.fn()
		const { getByRole } = render(<Button onPress={onPressMock} accessibilityLabel="Custom Accessibility" />)
		const button = getByRole('button')
		expect(button.props.accessibilityLabel).toBe('Custom Accessibility')
	})

	it('should render icon with default color when not provided', () => {
		const { getByTestId } = render(
			<Button
				label="Default Icon Color"
				icon={{
					library: 'AntDesign',
					name: 'star',
					size: 'medium',
					// No color provided, should use theme.colors.secondary
				}}
				testID="default-icon-color"
			/>
		)
		expect(getByTestId('default-icon-color')).toBeTruthy()
	})

	it('should apply custom textStyle', () => {
		const customTextStyle = { fontSize: 20, fontWeight: 'bold' }
		const { getByText } = render(<Button label="Custom Text Style" textStyle={customTextStyle} />)
		expect(getByText('Custom Text Style')).toBeTruthy()
	})

	it('should spread additional props to TouchableOpacity', () => {
		const onPressMock = jest.fn()
		const { getByRole } = render(
			<Button
				label="Additional Props"
				onPress={onPressMock}
				disabled={false}
				accessibilityHint="This is a hint"
			/>
		)
		const button = getByRole('button')
		expect(button).toBeTruthy()
		expect(button.props.accessibilityHint).toBe('This is a hint')
	})
})
