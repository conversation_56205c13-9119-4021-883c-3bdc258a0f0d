// Mock API service for testing
const apiService = {
	get: jest.fn(),
	post: jest.fn(),
	put: jest.fn(),
	delete: jest.fn(),
}

describe('API Services', () => {
	beforeEach(() => {
		jest.clearAllMocks()
	})

	test('should make GET request', async () => {
		apiService.get.mockResolvedValue({ data: 'test' })

		const result = await apiService.get('/test')

		expect(apiService.get).toHaveBeenCalledWith('/test')
		expect(result).toEqual({ data: 'test' })
	})

	test('should make POST request', async () => {
		const postData = { name: 'test' }
		apiService.post.mockResolvedValue({ data: 'created' })

		const result = await apiService.post('/test', postData)

		expect(apiService.post).toHaveBeenCalledWith('/test', postData)
		expect(result).toEqual({ data: 'created' })
	})
})
