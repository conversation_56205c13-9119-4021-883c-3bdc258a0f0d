import { act, renderHook } from '@testing-library/react-native'

import useApi from '../../src/stores/api.store'
import useTheme from '../../src/stores/theme.store'
import useUser from '../../src/stores/user.store'

// Mock das dependências
jest.mock('../../src/utils/api', () => ({
	__esModule: true,
	default: {
		get: jest.fn(),
		post: jest.fn(),
		put: jest.fn(),
		delete: jest.fn(),
	},
}))

jest.mock('../../src/helpers/AsyncStorage', () => ({
	setData: jest.fn(),
	getData: jest.fn(() => Promise.resolve(null)),
	removeData: jest.fn(),
}))

describe('Core Stores', () => {
	beforeEach(() => {
		jest.clearAllMocks()
	})

	describe('User Store', () => {
		test('should initialize with null user', () => {
			const { result } = renderHook(() => useUser())
			expect(result.current.user).toBeNull()
		})

		test('should set user correctly', () => {
			const { result } = renderHook(() => useUser())
			const mockUser = { id: 1, name: 'Test User', email: '<EMAIL>' }

			act(() => {
				result.current.setUser(mockUser)
			})

			expect(result.current.user).toEqual(mockUser)
		})

		test('should clear user correctly', () => {
			const { result } = renderHook(() => useUser())
			const mockUser = { id: 1, name: 'Test User', email: '<EMAIL>' }

			act(() => {
				result.current.setUser(mockUser)
			})

			act(() => {
				result.current.clearUser()
			})

			expect(result.current.user).toBeNull()
		})
	})

	describe('API Store', () => {
		test('should initialize with default loading state', () => {
			const { result } = renderHook(() => useApi())
			expect(result.current.loading).toBe(false)
		})

		test('should handle section loading correctly', () => {
			const { result } = renderHook(() => useApi())

			act(() => {
				result.current.setLoadingForSection('test', true)
			})

			expect(result.current.isSectionLoading('test')).toBe(true)
		})
	})

	describe('Theme Store', () => {
		test('should initialize with default theme', () => {
			const { result } = renderHook(() => useTheme())
			expect(result.current.theme).toBeDefined()
		})
	})
})
