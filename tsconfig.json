{"extends": "expo/tsconfig.base", "compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-jsx", "lib": ["dom", "esnext"], "moduleResolution": "bundler", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "strict": false, "target": "esnext", "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}